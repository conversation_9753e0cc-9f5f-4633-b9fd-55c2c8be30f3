﻿#define TMP

using System.Collections.Immutable;
using System.Numerics;

namespace DrawnUi.Draw
{
    public partial class SkiaScroll
    {
        //todo complete and move

        public override void UpdateByChild(SkiaControl control)
        {
            if (UseVirtual)
            {
                //todo somehow detect which plane is invalidated upon child on it
                return;

                PlaneCurrent?.Invalidate();
                PlaneForward?.Invalidate();
            }

            base.UpdateByChild(control);
        }

        //todo use when context size is bigger than 2 viewports?
        public virtual bool UseVirtual
        {
            get
            {
                return Content != null
                       && Orientation != ScrollOrientation.Both && Content is SkiaLayout layout && layout.Virtualisation == VirtualisationType.Managed;
            }
        }


        protected Plane PlaneCurrent { get; set; }
        protected Plane PlaneForward { get; set; }
        protected Plane PlaneBackward { get; set; }
        protected int _planeWidth;
        protected int _planeHeight;
        protected int _planePrepareThreshold;
        private float swappedDownAt;
        private float swappedUpAt;


        public override ScaledRect GetOnScreenVisibleArea(DrawingContext context, Vector2 inflateByPixels = default)
        {

            if (UseVirtual)
            {
                //todo
                if (context.GetArgument(ContextArguments.Viewport.ToString()) is SKRect insideViewport)
                {
                    //yay!!!!!
                    //we can return the plane rect!!!!
                    Debug.WriteLine($"UsePlanes area: {insideViewport}");

                    return ScaledRect.FromPixels(insideViewport, _zoomedScale);
                }

                return ScaledRect.FromPixels(context.Destination, _zoomedScale);
            }

            if (Virtualisation != VirtualisationType.Disabled) //true by default
            {
                //passing visible area to be rendered
                //when scrolling we will pass changed area to be rendered
                //most suitable for large content
                var inflated = ContentViewport.Pixels;
                inflated.Inflate(inflateByPixels.X, inflateByPixels.Y);
                return ScaledRect.FromPixels(inflated, RenderingScale);
            }
            else
            {
                //passing the whole area to be rendered.
                //when scrolling we will just translate it
                //most suitable for small content
                return ContentRectWithOffset;

                //absoluteViewPort = new SKRect(Viewport.Pixels.Left, Viewport.Pixels.Top,
                //    Viewport.Pixels.Left + ContentSize.Pixels.Width, Viewport.Pixels.Top + ContentSize.Pixels.Height);
            }
        }


        public virtual void InitializePlanes()
        {
            var viewportWidth = Viewport.Pixels.Width;
            var viewportHeight = Viewport.Pixels.Height;

            // Ensure the planes cover twice the viewport area
            _planeWidth = (int)(viewportWidth); //for vertical, todo all orientations
            _planeHeight = (int)(viewportHeight * 2);
            _planePrepareThreshold = (int)(_planeHeight / 2);

            float offsetX = 0, offsetY = 0;

            if (Orientation == ScrollOrientation.Vertical)
            {
                offsetY = _planeHeight;
            }
            else if (Orientation == ScrollOrientation.Horizontal)
            {
                offsetX = _planeWidth;
            }

            PlaneCurrent = new Plane
            {
                Id="Current",
                Surface = SKSurface.Create(new SKImageInfo(_planeWidth, _planeHeight)),
                BackgroundColor = SKColors.Red,
                Destination = new (0,0,_planeWidth, _planeHeight)
            };

            PlaneForward = new Plane
            {
                Id = "Forward",
                OffsetX = offsetX,
                OffsetY = offsetY,
                Surface = SKSurface.Create(new SKImageInfo(_planeWidth, _planeHeight)),
                Destination = new(0, 0, _planeWidth, _planeHeight),
                BackgroundColor = SKColors.Green,
            };

            PlaneBackward = new Plane
            {
                Id = "Backward",
                OffsetX = -offsetX,
                OffsetY = -offsetY,
                Surface = SKSurface.Create(new SKImageInfo(_planeWidth, _planeHeight)),
                Destination = new(0, 0, _planeWidth, _planeHeight),
                BackgroundColor = SKColors.Blue,
            };

        }

        //protected float _baseOffsetY;
        protected bool _planesInverted;
        protected void SwapPlanes()
        {
            // Swap the planes and mark the new PlaneB as not ready.
            (PlaneCurrent, PlaneForward) = (PlaneForward, PlaneCurrent);
            if (_planesInverted)
            {
                PlaneCurrent.Invalidate();
            }
            else
            {
                PlaneForward.Invalidate();
            }
            _planesInverted = !_planesInverted;
        }


        void SetContentVisibleDelegate()
        {
            if (Content != null && Content.DelegateGetOnScreenVisibleArea == null)
            {
                Content.DelegateGetOnScreenVisibleArea = ReportVisibleAreToContent;
            }
        }

        private ScaledRect ReportVisibleAreToContent(Vector2 arg)
        {
            return ScaledRect.FromPixels(PlaneCurrent.Destination, RenderingScale);
        }




        private int visibleAreaCaller = 0;
        protected bool _buildingPlaneB;
        protected bool _buildingPlaneC;
        private bool _availablePlaneC;
        private bool _availablePlaneB;
        private SemaphoreSlim _lockPlanesWorker = new(1);

        private readonly Dictionary<string, PlaneBuildState> _planeBuildStates
            = new Dictionary<string, PlaneBuildState>
            {
                { "Current",  new PlaneBuildState() },
                { "Forward",  new PlaneBuildState() },
                { "Backward", new PlaneBuildState() }
            };

        private class PlaneBuildState
        {
            public bool IsBuilding;                
            public CancellationTokenSource Cts;    
        }

        /// <summary>
        /// GLOBAL INFRASTRUCTURE: Cancel background rendering for a specific plane when it's being repositioned
        /// </summary>
        private void CancelBackgroundRenderingForPlane(string planeId)
        {
            if (_planeBuildStates.TryGetValue(planeId, out var state))
            {
                if (state.IsBuilding && state.Cts != null)
                {
                    var planeColor = GetPlaneColorName(planeId);
                    Debug.WriteLine($"🚫 {planeColor} CANCEL: Plane {planeId} background rendering canceled (repositioned/invalidated)");
                    state.Cts.Cancel();
                    state.IsBuilding = false;
                }
                else
                {
                    var planeColor = GetPlaneColorName(planeId);
                    Debug.WriteLine($"ℹ️ {planeColor} CANCEL: Plane {planeId} cancel requested but not building");
                }
            }
            else
            {
                Debug.WriteLine($"❌ CANCEL: Unknown planeId {planeId} - no state found");
            }
        }

        protected void TriggerPreparePlane(DrawingContext context, string planeId)
        {
            var planeColor = GetPlaneColorName(planeId);

            if (!_planeBuildStates.TryGetValue(planeId, out var state))
            {
                Debug.WriteLine($"❌ {planeColor} TriggerPreparePlane: Unknown planeId {planeId}");
                return;
            }

            // If this plane is already building, cancel the previous job
            if (state.IsBuilding && state.Cts != null)
            {
                Debug.WriteLine($"🚫 {planeColor} TriggerPreparePlane: Canceling previous build for {planeId}");
                state.Cts.Cancel();  // signal old task to stop
            }

            // ARCHITECTURAL FIX: Capture content offset BEFORE starting background rendering
            var plane = GetPlaneById(planeId);
            plane?.CaptureContentOffset();
            Debug.WriteLine($"📍 {planeColor} TriggerPreparePlane: Captured content offset for {planeId}: X={plane?.ContentOffsetX}, Y={plane?.ContentOffsetY}");

            // Create a fresh CTS and mark building
            state.Cts?.Dispose();
            state.Cts = new CancellationTokenSource();
            state.IsBuilding = true;
            var token = state.Cts.Token;

            Debug.WriteLine($"🚀 {planeColor} TriggerPreparePlane: STARTING background preparation for {planeId}");

            var clone = context; //always clone struct from arguments for another thread!
            Task.Run(async () =>
            {
                try
                {
                    await _planeLocks[planeId].WaitAsync(token);

                    if (token.IsCancellationRequested)
                    {
                        Debug.WriteLine($"🚫 {planeColor} Background task: {planeId} canceled before starting preparation");
                        return; // canceled before starting
                    }

                    // Now do the actual PreparePlane
                    var plane = GetPlaneById(planeId);
                    Debug.WriteLine($"⚙️ {planeColor} Background task: EXECUTING preparation for {planeId}");

                    PreparePlane(clone.WithArgument(new("BThread", true)), plane);

                    // CRITICAL FIX: Trigger invalidation after plane preparation completes
                    // This ensures the scroll container redraws to show the newly prepared plane
                    if (!token.IsCancellationRequested)
                    {
                        Debug.WriteLine($"✅ {planeColor} Background task: {planeId} preparation COMPLETED successfully, triggering repaint");
                        Repaint(); // Force redraw to show the prepared plane
                    }
                    else
                    {
                        Debug.WriteLine($"🚫 {planeColor} Background task: {planeId} preparation completed but was canceled - not triggering repaint");
                    }
                }
                catch (OperationCanceledException)
                {
                    Debug.WriteLine($"🚫 {planeColor} Background task: {planeId} preparation CANCELED (OperationCanceledException)");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ {planeColor} Background task: ERROR preparing {planeId}: {ex.Message}");
                }
                finally
                {
                    state.IsBuilding = false;
                    _planeLocks[planeId].Release();
                    Debug.WriteLine($"🏁 {planeColor} Background task: {planeId} preparation task finished, IsBuilding=false");
                }
            }, token).ConfigureAwait(false);
        }





        protected virtual Plane GetPlaneById(string planeId)
        {
            return planeId switch
            {
                "Current" => PlaneCurrent,
                "Forward" => PlaneForward,
                "Backward" => PlaneBackward,
                _ => throw new ArgumentException("Invalid plane ID", nameof(planeId))
            };
        }

        /// <summary>
        /// Get the debug color name for a plane ID to help with debugging
        /// RED=Current, GREEN=Forward, BLUE=Backward
        /// </summary>
        protected virtual string GetPlaneColorName(string planeId)
        {
            return planeId switch
            {
                "Current" => "RED",
                "Forward" => "GREEN",
                "Backward" => "BLUE",
                _ => "UNKNOWN"
            };
        }

        private readonly Dictionary<string, SemaphoreSlim> _planeLocks
            = new Dictionary<string, SemaphoreSlim>
            {
                { "Current",  new SemaphoreSlim(1,1) },
                { "Forward",  new SemaphoreSlim(1,1) },
                { "Backward", new SemaphoreSlim(1,1) }
            };


        /// <summary>
        /// Viewport scrolled
        /// </summary>
        protected virtual void OnScrolledForPlanes()
        {
            _availablePlaneB = true;

            if (Content is SkiaLayout layout && layout.IsTemplated
                                             && layout.MeasureItemsStrategy == MeasuringStrategy.MeasureVisible
                                             && layout.LastMeasuredIndex < layout.ItemsSource.Count)
            {
                var measuredEnd = layout.GetMeasuredContentEnd();

                double currentOffset = Orientation == ScrollOrientation.Vertical
                    ? -ViewportOffsetY
                    : -ViewportOffsetX;

                if (measuredEnd - currentOffset < 0)
                {
                    TriggerIncrementalMeasurement(layout);
                }

            }
        }

        /// <summary>
        /// CENTRALIZED PLANE PREPARATION LOGIC
        /// Handles cancellation of old rendering when planes are repositioned
        /// </summary>
        protected void EvaluateAndPreparePlanesAsNeeded(DrawingContext context)
        {
            var scrollDirection = GetScrollDirection();
            var isAtTop = ViewportOffsetY >= 0;

            // Calculate if we're at bottom: ViewportOffsetY is negative when scrolled down
            // At bottom: ViewportOffsetY <= -(ContentSize.Pixels.Height - Viewport.Pixels.Height)
            var maxScrollDown = -(ContentSize.Pixels.Height - Viewport.Pixels.Height);
            var isAtBottom = ViewportOffsetY <= maxScrollDown + 50; // Add 50px buffer for early preparation

            Debug.WriteLine($"🔍 SCROLL STATE: ViewportOffsetY={ViewportOffsetY:F1}, ContentHeight={ContentSize.Pixels.Height:F1}, ViewportHeight={Viewport.Pixels.Height:F1}, MaxScrollDown={maxScrollDown:F1}, IsAtBottom={isAtBottom}");

            // CENTRALIZED PLANE PREPARATION TUNING - ALL LOGIC IN ONE PLACE
            DecideAndPreparePlanes(context, scrollDirection, isAtTop, isAtBottom);
        }

        /// <summary>
        /// 🎯 CENTRALIZED PLANE PREPARATION TUNING - ALL LOGIC HERE FOR EASY TUNING
        /// TRUE PROACTIVE PREPARATION: Prepare ALL planes as soon as ANY scrolling happens
        /// </summary>
        private void DecideAndPreparePlanes(DrawingContext context, ScrollDirection scrollDirection, bool isAtTop, bool isAtBottom)
        {
            // � AGGRESSIVE PROACTIVE PREPARATION - Prepare ALL planes when ANY scrolling happens
            // Don't wait for specific scroll direction - prepare everything in advance!

            // CRITICAL SENIOR INSIGHT: Prepare planes based on POSITION THRESHOLDS
            // Don't wait for scrolling - prepare when position indicates need

            Debug.WriteLine($"🎯 DECIDE PLANES: scrollDirection={scrollDirection}, isAtTop={isAtTop}, isAtBottom={isAtBottom}, ViewportOffsetY={ViewportOffsetY}");
            Debug.WriteLine($"🔴 CURRENT PLANE: Id={PlaneCurrent?.Id}, OffsetY={PlaneCurrent?.OffsetY}, ContentOffsetY={PlaneCurrent?.ContentOffsetY}, IsReady={PlaneCurrent?.IsReady}");
            Debug.WriteLine($"🟢 FORWARD PLANE: Id={PlaneForward?.Id}, OffsetY={PlaneForward?.OffsetY}, ContentOffsetY={PlaneForward?.ContentOffsetY}, IsReady={PlaneForward?.IsReady}");
            Debug.WriteLine($"🔵 BACKWARD PLANE: Id={PlaneBackward?.Id}, OffsetY={PlaneBackward?.OffsetY}, ContentOffsetY={PlaneBackward?.ContentOffsetY}, IsReady={PlaneBackward?.IsReady}");

            // 🟢 FORWARD PLANE: Prepare when approaching or at bottom (for overscroll)
            var shouldPrepareForward = ShouldPrepareForwardPlane(isAtBottom, scrollDirection);
            Debug.WriteLine($"🟢 FORWARD DECISION: shouldPrepare={shouldPrepareForward} → {(shouldPrepareForward ? "WILL PREPARE" : "SKIP")} (isAtBottom={isAtBottom}, scrollDirection={scrollDirection})");

            if (shouldPrepareForward)
            {
                CheckAndPreparePlane(context, "Forward", PlaneForward);
            }

            // 🔵 BACKWARD PLANE: Prepare when approaching or at top (for overscroll)
            var shouldPrepareBackward = ShouldPrepareBackwardPlane(isAtTop, scrollDirection);
            Debug.WriteLine($"🔵 BACKWARD DECISION: shouldPrepare={shouldPrepareBackward} → {(shouldPrepareBackward ? "WILL PREPARE" : "SKIP")} (isAtTop={isAtTop}, scrollDirection={scrollDirection})");

            if (shouldPrepareBackward)
            {
                CheckAndPreparePlane(context, "Backward", PlaneBackward);
            }
        }

        /// <summary>
        /// Forward plane needed when at/near bottom OR scrolling down
        /// </summary>
        private bool ShouldPrepareForwardPlane(bool isAtBottom, ScrollDirection scrollDirection)
        {
            // Prepare Forward plane if:
            // 1. We're at the bottom (for overscroll) - CRITICAL FIX
            // 2. We're scrolling down (proactive preparation)
            return isAtBottom || scrollDirection == ScrollDirection.Down;
        }

        /// <summary>
        /// Backward plane needed when at/near top OR scrolling up
        /// </summary>
        private bool ShouldPrepareBackwardPlane(bool isAtTop, ScrollDirection scrollDirection)
        {
            // Prepare Backward plane if:
            // 1. We're at the top (for overscroll)
            // 2. We're scrolling up (proactive preparation)
            return isAtTop || scrollDirection == ScrollDirection.Up;
        }

        /// <summary>
        /// Proper plane state validation and preparation
        /// This is the correct way to handle plane preparation - check content validity
        /// </summary>
        private void CheckAndPreparePlane(DrawingContext context, string planeId, Plane plane)
        {
            if (plane == null)
            {
                Debug.WriteLine($"❌ {GetPlaneColorName(planeId)} CheckAndPreparePlane: {planeId} plane is NULL");
                return;
            }

            if (_planeBuildStates[planeId].IsBuilding)
            {
                Debug.WriteLine($"⏳ {GetPlaneColorName(planeId)} CheckAndPreparePlane: {planeId} plane already building - skipping");
                return;
            }

            var planeColor = GetPlaneColorName(planeId);

            // Determine if plane needs preparation
            bool needsPreparation = false;
            string reason = "";

            if (!plane.IsReady)
            {
                needsPreparation = true;
                reason = "not ready";
            }
            else if (!IsPlaneContentValidForCurrentPosition(plane))
            {
                needsPreparation = true;
                reason = $"content stale - prepared for Y:{plane.ContentOffsetY}, now at Y:{plane.OffsetY}";
            }

            if (needsPreparation)
            {
                Debug.WriteLine($"🎯 {planeColor} ✅ DECISION: PREPARE {planeId} plane - {reason}");
                CancelBackgroundRenderingForPlane(planeId);
                TriggerPreparePlane(context, planeId);
            }
            else
            {
                Debug.WriteLine($"✅ {planeColor} ⏭️ SKIP: {planeId} plane is ready and valid - no preparation needed");
            }
        }

        /// <summary>
        /// Content validity check
        /// Content is valid if it was prepared for the current plane position
        /// </summary>
        private bool IsPlaneContentValidForCurrentPosition(Plane plane)
        {
            // Content is valid if it was prepared for the current offset position
            // Allow small tolerance for floating point precision
            const float tolerance = 0.1f;
            return Math.Abs(plane.ContentOffsetY - plane.OffsetY) < tolerance;
        }

        /// <summary>
        /// Simple scroll direction detection
        /// </summary>
        private ScrollDirection GetScrollDirection()
        {
            // This is a simplified version - you might want to track previous scroll position
            // for more accurate direction detection
            if (ViewportOffsetY > 0) return ScrollDirection.Up;
            if (ViewportOffsetY < 0) return ScrollDirection.Down;
            return ScrollDirection.None;
        }

        private enum ScrollDirection
        {
            None,
            Up,
            Down
        }

        /// <summary>
        /// This is called when scrolling changes when in UseVirtual mode, override this to draw custom content
        /// </summary>
        /// <param name="context"></param>
        public virtual void DrawVirtual(DrawingContext context)
        {
            if (PlaneCurrent == null)
            {
                InitializePlanes();
                if (PlaneCurrent == null || PlaneForward == null || PlaneBackward == null)
                {
                    Super.Log("Failed to create planes");
                    return;
                }
            }
            var displayRectA = new SKRect(
                ContentRectWithOffset.Pixels.Left,
                ContentRectWithOffset.Pixels.Top,
                ContentRectWithOffset.Pixels.Left + _planeWidth,
                ContentRectWithOffset.Pixels.Top + _planeHeight
            );

            if (!PlaneCurrent.IsReady)
            {
                Debug.WriteLine("Preparing PLANE A..");
                PreparePlane(context.WithDestination(displayRectA), PlaneCurrent);
            }

            // CENTRALIZED PLANE PREPARATION LOGIC
            // Evaluate which planes need preparation based on current scroll state
            EvaluateAndPreparePlanesAsNeeded(context);

            // Draw the planes - capture scroll offset once to avoid race conditions
            var currentScroll = InternalViewportOffset.Pixels.Y;
            Debug.WriteLine($"🎨 DRAWING PLANES with currentScroll: {currentScroll}");

            // ARCHITECTURAL FIX: No more hardcoded workarounds needed
            // Content offset capture prevents race conditions at the source

            var rectBase = new SKRect(0, 0, _planeWidth, _planeHeight);
            rectBase.Offset(DrawingRect.Left, DrawingRect.Top);

            var rectCurrent = rectBase;
            var rectForward = rectBase;
            var rectBackward = rectBase;

            // Apply vertical offsets using consistent scroll value
            rectCurrent.Offset(0, currentScroll + PlaneCurrent.OffsetY);
            rectForward.Offset(0, currentScroll + PlaneForward.OffsetY);
            rectBackward.Offset(0, currentScroll + PlaneBackward.OffsetY);

            Debug.WriteLine($"🔍 PLANE POSITIONING: currentScroll={currentScroll}, ContentViewport={ContentViewport.Pixels}");
            Debug.WriteLine($"🔴 CURRENT: Id={PlaneCurrent.Id}, OffsetY={PlaneCurrent.OffsetY}, finalTop={currentScroll + PlaneCurrent.OffsetY}, rect={rectCurrent}");
            Debug.WriteLine($"🟢 FORWARD: Id={PlaneForward.Id}, OffsetY={PlaneForward.OffsetY}, finalTop={currentScroll + PlaneForward.OffsetY}, rect={rectForward}");
            Debug.WriteLine($"🔵 BACKWARD: Id={PlaneBackward.Id}, OffsetY={PlaneBackward.OffsetY}, finalTop={currentScroll + PlaneBackward.OffsetY}, rect={rectBackward}");

            //Debug.WriteLine($"Plane positions - Current: {rectCurrent}, Forward: {rectForward}, Backward: {rectBackward}");

            //  if we've moved enough can re-allow a swap
            if (swappedDownAt != 0 && Math.Abs(currentScroll - swappedDownAt) > _planeHeight / 2f)
            {
                swappedDownAt = 0;
            }
            if (swappedUpAt != 0 && Math.Abs(currentScroll - swappedUpAt) > _planeHeight / 2f)
            {
                swappedUpAt = 0;
            }

            // Draw Backward
            if (PlaneBackward.IsReady)
            {
                if (ContentViewport.Pixels.IntersectsWithInclusive(rectBackward))
                {
                    PlaneBackward.CachedObject.Draw(context.Context.Canvas, rectBackward.Left, rectBackward.Top, null);
                    PlaneBackward.LastDrawnAt = rectBackward;
                }
            }
            else
            {
                PlaneBackward.CachedObject?.Draw(context.Context.Canvas, rectBackward.Left, rectBackward.Top, null);
            }

            // Draw Current
            if (ContentViewport.Pixels.IntersectsWith(rectCurrent))
            {
                PlaneCurrent.CachedObject.Draw(context.Context.Canvas, rectCurrent.Left, rectCurrent.Top, null);
                PlaneCurrent.LastDrawnAt = rectCurrent;
            }

            // Draw Forward
            Debug.WriteLine($"🟢 FORWARD DRAW CHECK: Id={PlaneForward.Id}, IsReady={PlaneForward.IsReady}, rectForward={rectForward}");

            if (PlaneForward.IsReady)
            {
                var intersects = ContentViewport.Pixels.IntersectsWith(rectForward);
                Debug.WriteLine($"🟢 FORWARD PLANE READY - intersection check: {intersects} (rectForward={rectForward} vs ContentViewport={ContentViewport.Pixels})");

                if (intersects)
                {
                    Debug.WriteLine($"🟢 ✅ DRAWING FORWARD PLANE at {rectForward.Left}, {rectForward.Top}");
                    PlaneForward.CachedObject.Draw(context.Context.Canvas, rectForward.Left, rectForward.Top, null);
                    PlaneForward.LastDrawnAt = rectForward;
                }
                else
                {
                    Debug.WriteLine($"🟢 ❌ FORWARD PLANE READY but NO INTERSECTION - not drawing (plane outside viewport)");
                }
            }
            else
            {
                Debug.WriteLine($"🟢 ⏳ FORWARD PLANE NOT READY - drawing cached object for fast scrolling");
                PlaneForward.CachedObject?.Draw(context.Context.Canvas, rectForward.Left, rectForward.Top, null); //repeat last image for fast scrolling
            }

            // --------------------------------------------------------------------
            // Multiple-swap logic to handle fast scrolling
            // --------------------------------------------------------------------

            int swaps = 0;
            bool swappedSomething = false;
            while (!swappedSomething)
            {
                // ------------------------------------------------------
                // then swap down as many times as needed
                // ------------------------------------------------------
                var topDown = -1f; // break when same 
                while (topDown != rectForward.Top && rectForward.MidY <= (Viewport.Pixels.Height / 2))
                {
                    topDown = rectForward.Top;
                    //if (swappedDownAt != 0)
                    //    break;

                    SwapDown();
                    swaps++;
                    swappedDownAt = currentScroll;
                    swappedSomething = true;

                    rectForward = rectBase;
                    rectForward.Offset(0, currentScroll + PlaneForward.OffsetY);
                }

                // ------------------------------------------------------
                // swap up as many times as needed
                // ------------------------------------------------------
                var topUp = -1f;
                while (topUp != rectBackward.Top && rectBackward.MidY > Viewport.Pixels.Height / 2)
                {
                    topUp = rectBackward.Top;
                    //if (swappedUpAt != 0)
                    //    break;

                    SwapUp();
                    swaps++;
                    swappedUpAt = currentScroll;
                    swappedSomething = true;

                    rectBackward = rectBase;
                    rectBackward.Offset(0, currentScroll - rectBackward.Top);
                }

                if (!swappedSomething)
                    break;
            }


        }



        // -----------------------------------------------------------
        // SWAP LOGIC
        // -----------------------------------------------------------
        private void SwapDown()
        {
            Debug.WriteLine($"🔄 ⬇️ SWAP DOWN BEFORE: Current={PlaneCurrent?.Id}, Forward={PlaneForward?.Id}, Backward={PlaneBackward?.Id}");

            // forward ↑ current
            // current ↑ backward
            // backward ↓ forward + invalidate
            var temp = PlaneBackward;
            PlaneBackward = PlaneCurrent;
            PlaneCurrent = PlaneForward;
            PlaneForward = temp;

            Debug.WriteLine($"🔄 ⬇️ SWAP DOWN AFTER: Current={PlaneCurrent?.Id}, Forward={PlaneForward?.Id}, Backward={PlaneBackward?.Id}");
            Debug.WriteLine($"🔄 ⬇️ NEW POSITIONS: Forward.OffsetY={PlaneForward.OffsetY}, Backward.OffsetY={PlaneBackward.OffsetY}");

            // CRITICAL FIX: Cancel background rendering for plane being repositioned
            CancelBackgroundRenderingForPlane("Forward");

            // SIMPLE LOGIC: Position forward plane to show content after current plane
            PlaneForward.OffsetY = PlaneCurrent.OffsetY + _planeHeight;

            PlaneBackward.OffsetY = PlaneCurrent.OffsetY - _planeHeight;
            PlaneForward.Invalidate();
        }

        private void SwapUp()
        {
            Debug.WriteLine($"🔄 ⬆️ SWAP UP BEFORE: Current={PlaneCurrent?.Id}, Forward={PlaneForward?.Id}, Backward={PlaneBackward?.Id}");

            var temp = PlaneForward;
            PlaneForward = PlaneCurrent;
            PlaneCurrent = PlaneBackward;
            PlaneBackward = temp;

            Debug.WriteLine($"🔄 ⬆️ SWAP UP AFTER: Current={PlaneCurrent?.Id}, Forward={PlaneForward?.Id}, Backward={PlaneBackward?.Id}");
            Debug.WriteLine($"🔄 ⬆️ NEW POSITIONS: Forward.OffsetY={PlaneForward.OffsetY}, Backward.OffsetY={PlaneBackward.OffsetY}");

            // CRITICAL FIX: Cancel background rendering for plane being repositioned
            CancelBackgroundRenderingForPlane("Backward");

            PlaneBackward.OffsetY = PlaneCurrent.OffsetY - _planeHeight;
            PlaneForward.OffsetY = PlaneCurrent.OffsetY + _planeHeight;
            PlaneBackward.Invalidate();
        }



        protected virtual void PreparePlane(DrawingContext context, Plane plane)
        {
            var destination = plane.Destination;

            var recordingContext = context.CreateForRecordingImage(plane.Surface, destination.Size);

            var viewport = plane.Destination;
            viewport.Offset(InternalViewportOffset.Pixels.X, InternalViewportOffset.Pixels.Y);
            viewport.Offset(DrawingRect.Left, DrawingRect.Top);
            // ARCHITECTURAL FIX: Use captured content offset, not dynamic offset
            viewport.Offset(plane.ContentOffsetX, plane.ContentOffsetY);

            var c = recordingContext.Context.Canvas.Save();
            recordingContext.Context.Canvas.Translate(-viewport.Left, -viewport.Top);
            recordingContext.Context.Canvas.Clear(plane.BackgroundColor);

            PaintOnPlane(recordingContext
                .WithDestination(viewport)
                .WithArguments(
                    new(ContextArguments.Plane.ToString(), plane.Id),
                    new(ContextArguments.Viewport.ToString(), viewport)), plane);

            recordingContext.Context.Canvas.RestoreToCount(c);

            recordingContext.Context.Canvas.Flush();
            DisposeObject(plane.CachedObject);
            plane.CachedObject = new CachedObject(
                SkiaCacheType.Image,
                plane.Surface,
                new SKRect(0, 0, _planeWidth, _planeHeight),
                destination)
            {
                PreserveSourceFromDispose = true
            };

            plane.IsReady = true;
        }
        


        protected virtual void PaintOnPlane(DrawingContext context, Plane plane)
        {
            PaintViews(context);
        }

    }
}
