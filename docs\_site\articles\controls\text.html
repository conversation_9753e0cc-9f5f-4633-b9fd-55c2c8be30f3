<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Text Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Text Controls | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../public/docfx.min.css">
      <link rel="stylesheet" href="../../public/main.css">
      <meta name="docfx:navrel" content="../../toc.html">
      <meta name="docfx:tocrel" content="../toc.html">
      
      <meta name="docfx:rel" content="../../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/text.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../../index.html">
            <img id="logo" class="svg" src="../../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="text-controls">Text Controls</h1>

<p>DrawnUi.Maui offers text rendering capabilities through its specialized text controls. These controls provide text rendering with advanced formatting options while maintaining consistent appearance across all platforms.</p>
<h2 id="skialabel">SkiaLabel</h2>
<p>SkiaLabel is the primary text rendering control in DrawnUi.Maui, rendering text directly with SkiaSharp. Unlike traditional MAUI labels, SkiaLabel provides pixel-perfect text rendering with advanced formatting capabilities.</p>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaLabel
    Text=&quot;Hello World&quot;
    TextColor=&quot;Black&quot;
    FontSize=&quot;18&quot;
    HorizontalTextAlignment=&quot;Center&quot;
    VerticalTextAlignment=&quot;Center&quot; /&gt;
</code></pre>
<h3 id="key-properties">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Text</code></td>
<td>string</td>
<td>The text content to display</td>
</tr>
<tr>
<td><code>TextColor</code></td>
<td>Color</td>
<td>Text color</td>
</tr>
<tr>
<td><code>FontFamily</code></td>
<td>string</td>
<td>Font family name</td>
</tr>
<tr>
<td><code>FontSize</code></td>
<td>float</td>
<td>Font size in logical pixels</td>
</tr>
<tr>
<td><code>FontWeight</code></td>
<td>int</td>
<td>Font weight (100-900 scale, 400=normal, 700=bold)</td>
</tr>
<tr>
<td><code>FontAttributes</code></td>
<td>FontAttributes</td>
<td>Bold/Italic/None</td>
</tr>
<tr>
<td><code>HorizontalTextAlignment</code></td>
<td>DrawTextAlignment</td>
<td>Text horizontal alignment (Start, Center, End, Fill)</td>
</tr>
<tr>
<td><code>VerticalTextAlignment</code></td>
<td>DrawTextAlignment</td>
<td>Text vertical alignment (Start, Center, End)</td>
</tr>
<tr>
<td><code>LineBreakMode</code></td>
<td>LineBreakMode</td>
<td>How text should wrap or truncate</td>
</tr>
<tr>
<td><code>MaxLines</code></td>
<td>int</td>
<td>Maximum number of lines to display</td>
</tr>
<tr>
<td><code>StrokeColor</code></td>
<td>Color</td>
<td>Outline color</td>
</tr>
<tr>
<td><code>StrokeWidth</code></td>
<td>double</td>
<td>Outline width</td>
</tr>
<tr>
<td><code>DropShadowColor</code></td>
<td>Color</td>
<td>Shadow color</td>
</tr>
<tr>
<td><code>DropShadowSize</code></td>
<td>double</td>
<td>Shadow blur radius</td>
</tr>
<tr>
<td><code>DropShadowOffsetX</code>/<code>DropShadowOffsetY</code></td>
<td>double</td>
<td>Shadow offset</td>
</tr>
<tr>
<td><code>AutoSize</code></td>
<td>AutoSizeType</td>
<td>Auto-sizing mode</td>
</tr>
<tr>
<td><code>AutoSizeText</code></td>
<td>string</td>
<td>Text to use for auto-sizing calculations</td>
</tr>
<tr>
<td><code>LineSpacing</code></td>
<td>double</td>
<td>Line spacing multiplier</td>
</tr>
<tr>
<td><code>ParagraphSpacing</code></td>
<td>double</td>
<td>Paragraph spacing multiplier</td>
</tr>
<tr>
<td><code>CharacterSpacing</code></td>
<td>double</td>
<td>Character spacing multiplier</td>
</tr>
<tr>
<td><code>IsMonospaced</code></td>
<td>bool</td>
<td>Enables monospaced text rendering</td>
</tr>
<tr>
<td><code>MonoForDigits</code></td>
<td>string</td>
<td>Use mono width for digits (e.g. &quot;8&quot;)</td>
</tr>
</tbody>
</table>
<h3 id="rich-text-formatting-spans">Rich Text Formatting (Spans)</h3>
<p>SkiaLabel supports rich text formatting through its <code>Spans</code> collection:</p>
<pre><code class="lang-xml">&lt;draw:SkiaLabel&gt;
    &lt;draw:SkiaLabel.Spans&gt;
        &lt;draw:TextSpan Text=&quot;Hello &quot; TextColor=&quot;Black&quot; FontSize=&quot;18&quot; /&gt;
        &lt;draw:TextSpan Text=&quot;Beautiful &quot; TextColor=&quot;Red&quot; FontSize=&quot;20&quot; FontWeight=&quot;700&quot; /&gt;
        &lt;draw:TextSpan Text=&quot;World!&quot; TextColor=&quot;Blue&quot; FontSize=&quot;18&quot; FontAttributes=&quot;Italic&quot; /&gt;
    &lt;/draw:SkiaLabel.Spans&gt;
&lt;/draw:SkiaLabel&gt;
</code></pre>
<h4 id="interactive-spans">Interactive Spans</h4>
<p>You can make any text span interactive by adding the <code>Tapped</code> event handler:</p>
<pre><code class="lang-xml">&lt;draw:SkiaLabel FontSize=&quot;15&quot; LineSpacing=&quot;1.5&quot; TextColor=&quot;Black&quot;&gt;
    &lt;draw:TextSpan Text=&quot;Regular text &quot; /&gt;
    &lt;draw:TextSpan
        Text=&quot;tappable link&quot;
        TextColor=&quot;Purple&quot;
        Tapped=&quot;OnSpanTapped&quot;
        Tag=&quot;link-id&quot;
        Underline=&quot;True&quot; /&gt;
    &lt;draw:TextSpan Text=&quot; more text...&quot; /&gt;
&lt;/draw:SkiaLabel&gt;
</code></pre>
<p>In your code-behind:</p>
<pre><code class="lang-csharp">private void OnSpanTapped(object sender, EventArgs e)
{
    var span = sender as TextSpan;
    string tag = span?.Tag?.ToString();
    // Handle the tap event based on the span or its tag
}
</code></pre>
<h4 id="styling-spans">Styling Spans</h4>
<p>TextSpan supports various styling options:</p>
<pre><code class="lang-xml">&lt;draw:TextSpan Text=&quot;Bold text&quot; FontAttributes=&quot;Bold&quot; /&gt;
&lt;draw:TextSpan Text=&quot;Italic text&quot; FontAttributes=&quot;Italic&quot; /&gt;
&lt;draw:TextSpan Text=&quot;Underlined text&quot; Underline=&quot;True&quot; /&gt;
&lt;draw:TextSpan Text=&quot;Strikethrough text&quot; Strikeout=&quot;True&quot; /&gt;
&lt;draw:TextSpan Text=&quot;Highlighted text&quot; BackgroundColor=&quot;Yellow&quot; /&gt;
</code></pre>
<h4 id="emoji-support">Emoji Support</h4>
<p>For emoji rendering, use the <code>AutoFont</code> property:</p>
<pre><code class="lang-xml">&lt;draw:TextSpan Text=&quot;Regular text &quot; /&gt;
&lt;draw:TextSpan AutoFont=&quot;True&quot; Text=&quot;🌐🚒🙎🏽👻🤖&quot; /&gt;
&lt;draw:TextSpan Text=&quot; more text...&quot; /&gt;
</code></pre>
<p>This ensures proper emoji rendering by finding and using appropriate fonts.</p>
<h3 id="text-effects">Text Effects</h3>
<p>SkiaLabel supports various text effects:</p>
<h4 id="drop-shadow">Drop Shadow</h4>
<p>Use the following properties for shadow effects:</p>
<ul>
<li><code>DropShadowColor</code>: Shadow color</li>
<li><code>DropShadowSize</code>: Blur radius</li>
<li><code>DropShadowOffsetX</code>, <code>DropShadowOffsetY</code>: Shadow offset</li>
</ul>
<pre><code class="lang-xml">&lt;draw:SkiaLabel
    Text=&quot;Shadowed Text&quot;
    FontSize=&quot;24&quot;
    TextColor=&quot;White&quot;
    DropShadowColor=&quot;#80000000&quot;
    DropShadowSize=&quot;3&quot;
    DropShadowOffsetX=&quot;1&quot;
    DropShadowOffsetY=&quot;1&quot; /&gt;
</code></pre>
<h4 id="outlined-text">Outlined Text</h4>
<pre><code class="lang-xml">&lt;draw:SkiaLabel
    Text=&quot;Outlined Text&quot;
    FontSize=&quot;24&quot;
    TextColor=&quot;White&quot;
    StrokeColor=&quot;Black&quot;
    StrokeWidth=&quot;1&quot; /&gt;
</code></pre>
<h4 id="gradient-text">Gradient Text</h4>
<pre><code class="lang-xml">&lt;draw:SkiaLabel
    Text=&quot;Gradient Text&quot;
    FontSize=&quot;24&quot;
    FillGradient=&quot;{StaticResource MyGradient}&quot; /&gt;
</code></pre>
<h3 id="auto-sizing-text">Auto-sizing Text</h3>
<p>SkiaLabel features powerful automatic font sizing capabilities that can dynamically adjust text to fit your container:</p>
<pre><code class="lang-xml">&lt;draw:SkiaLabel
    Text=&quot;This text will resize to fit the available space&quot;
    AutoSize=&quot;TextToView&quot;
    FontSize=&quot;24&quot;
    MaxLines=&quot;1&quot; /&gt;
</code></pre>
<ul>
<li><code>AutoSize</code>: Controls auto-sizing mode (None, TextToWidth, TextToHeight, TextToView)</li>
<li><code>AutoSizeText</code>: Text to use for sizing calculations</li>
</ul>
<h3 id="monospaced-text-rendering">Monospaced Text Rendering</h3>
<p>SkiaLabel provides the ability to render text in a monospaced style, regardless of the font used:</p>
<pre><code class="lang-xml">&lt;draw:SkiaLabel
    Text=&quot;This text will be monospaced&quot;
    FontSize=&quot;18&quot;
    MonoForDigits=&quot;8&quot; /&gt;
</code></pre>
<ul>
<li><code>MonoForDigits</code>: Use mono width for digits (e.g. &quot;8&quot;)</li>
</ul>
<h3 id="performance-considerations">Performance Considerations</h3>
<ul>
<li>For static text, set <code>Cache=&quot;Image&quot;</code> to render once and cache as bitmap</li>
<li>For frequently updated text, use <code>Cache=&quot;Operations&quot;</code> for best performance</li>
<li>Consider setting <code>MaxLines</code> when appropriate to avoid unnecessary layout calculations</li>
<li>For large blocks of text, monitor performance and consider breaking into multiple labels</li>
<li>Use monospaced features only when needed as it adds some calculation overhead</li>
<li>For complex shadow effects, consider using <code>Cache=&quot;Image&quot;</code> to optimize rendering</li>
</ul>
<h2 id="skiarichlabel">SkiaRichLabel</h2>
<p>SkiaRichLabel extends SkiaLabel to provide Markdown formatting capabilities. It parses Markdown syntax and renders properly formatted text.</p>
<h3 id="basic-usage-1">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaRichLabel&gt;
# Markdown Title

This is a paragraph with **bold** and *italic* text.

- List item 1
- List item 2

[Visit Documentation](https://link.example.com)

`Inline code` looks like this.

```csharp
// Code block
var label = new SkiaRichLabel();
</code></pre>
<p>&lt;/draw:SkiaRichLabel&gt;</p>
<pre><code>
### Supported Markdown Features

- **Headings** (# H1, ## H2)
- **Text formatting** (bold, italic, strikethrough)
- **Lists** (bulleted and numbered)
- **Links** (with customizable styling)
- **Code** (inline and blocks)
- **Paragraphs** (with proper spacing)

### Customizing Markdown Style

```xml
&lt;draw:SkiaRichLabel
    LinkColor=&quot;Blue&quot;
    CodeTextColor=&quot;DarkGreen&quot;
    CodeBackgroundColor=&quot;#EEEEEE&quot;
    StrikeoutColor=&quot;Red&quot;
    PrefixBullet=&quot;• &quot;
    PrefixNumbered=&quot;{0}. &quot;
    UnderlineLink=&quot;True&quot;
    UnderlineWidth=&quot;1&quot;&gt;
# Custom Styled Markdown
This has **custom** styling for [links](https://example.com) and `code blocks`.
&lt;/draw:SkiaRichLabel&gt;
</code></pre>
<h3 id="link-handling">Link Handling</h3>
<p>SkiaRichLabel provides built-in support for handling link taps:</p>
<pre><code class="lang-xml">&lt;draw:SkiaRichLabel
    LinkTapped=&quot;OnLinkTapped&quot;
    CommandLinkTapped=&quot;{Binding OpenLinkCommand}&quot;&gt;
Check out [this link](https://example.com)!
&lt;/draw:SkiaRichLabel&gt;
</code></pre>
<p>In your code-behind:</p>
<pre><code class="lang-csharp">private void OnLinkTapped(object sender, string url)
{
    // url parameter contains the link URL
    Browser.OpenAsync(url);
}
</code></pre>
<h3 id="implementation-notes">Implementation Notes</h3>
<ul>
<li>SkiaRichLabel implements a lightweight Markdown parser optimized for display, not full CommonMark compliance</li>
<li>The parser focuses on the most commonly used Markdown syntax for mobile applications</li>
<li>For more complex Markdown rendering needs, consider creating a custom renderer</li>
</ul>
<h2 id="special-labels">Special Labels</h2>
<h3 id="skialabelfps">SkiaLabelFps</h3>
<p>A specialized label for displaying frames-per-second (FPS) metrics, useful for performance monitoring during development:</p>
<pre><code class="lang-xml">&lt;draw:SkiaLabelFps
    TextColor=&quot;Green&quot;
    FontSize=&quot;12&quot;
    HorizontalOptions=&quot;End&quot;
    VerticalOptions=&quot;Start&quot;
    Margin=&quot;0,20,20,0&quot; /&gt;
</code></pre>
<h2 id="example-text-card">Example: Text Card</h2>
<pre><code class="lang-xml">&lt;draw:SkiaShape
    Type=&quot;Rectangle&quot;
    BackgroundColor=&quot;White&quot;
    CornerRadius=&quot;8&quot;
    Padding=&quot;16&quot;
    WidthRequest=&quot;300&quot;&gt;

    &lt;draw:SkiaShape.Shadows&gt;
        &lt;draw:SkiaShadow
            Color=&quot;#22000000&quot;
            BlurRadius=&quot;10&quot;
            Offset=&quot;0,2&quot; /&gt;
    &lt;/draw:SkiaShape.Shadows&gt;

    &lt;draw:SkiaLayout Type=&quot;Column&quot; Spacing=&quot;8&quot;&gt;
        &lt;draw:SkiaLabel
            Text=&quot;Article Title&quot;
            FontSize=&quot;20&quot;
            FontWeight=&quot;700&quot;
            TextColor=&quot;#333333&quot; /&gt;

        &lt;draw:SkiaLabel
            Text=&quot;Published on April 3, 2025&quot;
            FontSize=&quot;12&quot;
            TextColor=&quot;#666666&quot; /&gt;

        &lt;draw:SkiaLabel
            Text=&quot;Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus rhoncus ut eleifend nibh porttitor.&quot;
            FontSize=&quot;14&quot;
            TextColor=&quot;#444444&quot;
            LineHeight=&quot;1.5&quot; /&gt;

        &lt;draw:SkiaLabel&gt;
            &lt;draw:SkiaLabel.Spans&gt;
                &lt;draw:TextSpan Text=&quot;Read more &quot; TextColor=&quot;#444444&quot; FontSize=&quot;14&quot; /&gt;
                &lt;draw:TextSpan Text=&quot;here&quot; TextColor=&quot;Blue&quot; FontSize=&quot;14&quot; IsUnderline=&quot;True&quot; /&gt;
            &lt;/draw:SkiaLabel.Spans&gt;
        &lt;/draw:SkiaLabel&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaShape&gt;
</code></pre>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/text.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
