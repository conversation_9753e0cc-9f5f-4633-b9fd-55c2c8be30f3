﻿<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.ScreenVarious"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:navigation1="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views.Controls.Navigation"
    xmlns:viewModels="using:AppoMobi.Maui.DrawnUi.Demo.ViewModels"
    x:DataType="viewModels:SimplePageViewModel"
    BackgroundColor="Black"
    HorizontalOptions="Fill"
    Spacing="0"
    Split="1"
    Type="Wrap"
    VerticalOptions="Fill">

    <draw:SkiaControl.FillGradient>

        <draw:SkiaGradient
            EndXRatio="1"
            EndYRatio="1"
            StartXRatio="0"
            StartYRatio="0"
            Type="Linear">
            <draw:SkiaGradient.Colors>
                <Color>#889955</Color>
                <Color>#886655</Color>
                <Color>#222222</Color>
            </draw:SkiaGradient.Colors>
        </draw:SkiaGradient>

    </draw:SkiaControl.FillGradient>


    <!--  STATUS BAR  -->
    <draw:SkiaShape
        BackgroundColor="Black"
        HeightRequest="{Binding Presentation.StatusBarHeightRequest}"
        HorizontalOptions="Fill"
        Tag="StatusBar" />

    <!--  NAVBAR  -->
    <navigation1:NavBarStandart />

    <!--  CONTENT  -->
    <draw:SkiaScroll HorizontalOptions="Fill" VerticalOptions="Fill">

        <draw:SkiaLayout
            Padding="16"
            HorizontalOptions="Fill"
            Spacing="24"
            Split="1"
            Tag="StackContent"
            Type="Wrap">


            <!--  ENTRY with FRAME  -->
            <draw:SkiaLayout
                Margin="24,24,24,0"
                HorizontalOptions="Fill"
                Tag="StackEntry">

                <draw:SkiaShape
                    BackgroundColor="{StaticResource ColorPrimaryDark}"
                    CornerRadius="6"
                    HeightRequest="40"
                    HorizontalOptions="Fill"
                    StrokeColor="{StaticResource Gray600}"
                    StrokeWidth="1"
                    UseCache="GPU"
                    VerticalOptions="Center"
                    ZIndex="-1">

                    <draw:SkiaSvg
                        Margin="11,0,0,0"
                        LockRatio="1"
                        SvgString="{x:StaticResource SvgSearch}"
                        TintColor="{StaticResource Gray400}"
                        VerticalOptions="Center"
                        WidthRequest="19" />

                </draw:SkiaShape>

                <draw:SkiaMauiEntry
                    Margin="40,2,8,0"
                    AnimateSnapshot="True"
                    CommandOnTextChanged="{Binding CommandTestVoid}"
                    FontFamily="FontText"
                    FontSize="16"
                    FontWeight="600"
                    HeightRequest="28"
                    HorizontalOptions="Fill"
                    KeyboardType="Chat"
                    MaxLines="1"
                    ReturnType="Search"
                    Tag="SearchEntry"
                    Text="{Binding Search}"
                    TextColor="{StaticResource ColorText}"
                    TextSubmitted="Editor_TextSubmitted"
                    VerticalOptions="Center" />

            </draw:SkiaLayout>

            <!--  EDITOR  -->
            <draw:SkiaShape
                Margin="24,24,24,0"
                Padding="6"
                BackgroundColor="Gainsboro"
                CornerRadius="8"
                HeightRequest="120"
                HorizontalOptions="Fill"
                StrokeColor="Gray"
                StrokeWidth="1"
                Tag="WrapEditor">

                <draw:SkiaMauiEditor
                    AnimateSnapshot="True"
                    BackgroundColor="White"
                    FontFamily="FontText"
                    FontSize="16"
                    FontWeight="600"
                    HorizontalOptions="Fill"
                    ReturnType="Default"
                    Tag="Editor"
                    Text="{Binding SomeText}"
                    TextColor="Black"
                    VerticalOptions="Fill" />

            </draw:SkiaShape>

            <draw:SkiaControl
                BackgroundColor="#886655"
                HeightRequest="1"
                HorizontalOptions="Fill" />

            <draw:SkiaRichLabel
                Margin="24,0,24,0"
                HorizontalOptions="Fill"
                HorizontalTextAlignment="Start"
                Style="{StaticResource SkiaLabelDefaultStyle}"
                Text="`SkiaMauiEntry` and *SkiaMauiEditor* are both a subclassed *SkiaMauiElement* with native input controls inside to be consumed inside a totally drawn app."
                UseCache="GPU" />

            <draw:SkiaControl HeightRequest="{Binding Presentation.BottomTabsUnderPadding}" />

        </draw:SkiaLayout>

    </draw:SkiaScroll>


</draw:SkiaLayout>
