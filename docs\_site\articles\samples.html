<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Samples | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Samples | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/samples.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="samples">Samples</h1>

<p>Explore real-world examples and code snippets to help you get started and master DrawnUi.Maui.</p>
<h2 id="example-apps">Example Apps</h2>
<h3 id="complete-demo-applications">Complete Demo Applications</h3>
<ul>
<li><p><strong><a href="https://github.com/taublast/AppoMobi.Maui.DrawnUi.Demo">Engine Demo</a></strong> 🤩 - A comprehensive totally drawn app demo featuring:</p>
<ul>
<li>Recycled cells and virtualization</li>
<li>Camera integration examples</li>
<li>Custom controls showcase</li>
<li>Updated with latest NuGet package</li>
</ul>
</li>
<li><p><strong><a href="https://github.com/taublast/AppoMobi.Maui.DrawnUi.SpaceShooter">Space Shooter Game</a></strong> 🎮 - Dynamic arcade game demonstrating:</p>
<ul>
<li>Full keyboard support implementation</li>
<li>Game-specific UI patterns</li>
<li>High-performance rendering</li>
<li>Uses preview NuGet with SkiaSharp v3</li>
</ul>
</li>
<li><p><strong><a href="https://github.com/taublast/ShadersCarousel/">Shaders Carousel</a></strong> ✨ - Advanced SkiaSharp v3 capabilities:</p>
<ul>
<li>Custom shader effects</li>
<li>Visual effects showcase</li>
<li>Hardware acceleration examples</li>
<li>Modern rendering techniques</li>
</ul>
</li>
</ul>
<h3 id="learning-projects">Learning Projects</h3>
<ul>
<li><p><strong><a href="https://github.com/taublast/DrawnUi.Maui/tree/main/src/Maui/Samples/Sandbox">Sandbox Project</a></strong> 🧪 - Experiment with:</p>
<ul>
<li>Pre-built drawn controls</li>
<li>Playground examples</li>
<li>Custom controls development</li>
<li>Maps integration</li>
<li>Various styling approaches</li>
</ul>
</li>
<li><p><strong><a href="https://github.com/taublast/SurfAppCompareDrawn">Drawn CollectionView Demo</a></strong> 📱 - Learn how to:</p>
<ul>
<li>Convert existing recycled cells lists to drawn ones</li>
<li>Compare performance benefits</li>
<li>Implement virtualization patterns</li>
<li>Optimize list rendering</li>
</ul>
</li>
</ul>
<h2 id="code-snippets">Code Snippets</h2>
<ul>
<li><a href="advanced/game-ui.html">Game UI &amp; Interactive Games</a></li>
<li><a href="advanced/gradients.html">Gradients</a></li>
<li><a href="advanced/skiascroll.html">SkiaScroll &amp; Virtualization</a></li>
<li><a href="advanced/gestures.html">Gestures &amp; Touch Input</a></li>
</ul>
<h2 id="production-apps">Production Apps</h2>
<p>Real-world applications built with DrawnUI:</p>
<h3 id="bug-id-insect-identifier-ai">Bug ID: Insect Identifier AI</h3>
<p><em>Totally drawn with just one root view <code>Canvas</code> and <code>SkiaShell</code> for navigation. First ever totally drawn published MAUI app.</em></p>
<ul>
<li><strong>Video Demo</strong>: <a href="https://www.youtube.com/watch?v=5QIaM0xsLbA">Watch on YouTube</a></li>
<li><strong>Architecture</strong>: Single Canvas root with SkiaShell navigation</li>
<li><strong>Features</strong>: AI-powered insect identification with fully drawn UI</li>
</ul>
<h3 id="racebox---vehicle-dynamics">Racebox - Vehicle Dynamics</h3>
<p><em>MAUI native pages with canvases and custom navigation. All scrolls, cells collections, maps, buttons, labels and custom controls are drawn.</em></p>
<ul>
<li><strong>Video Demo</strong>: <a href="https://www.youtube.com/watch?v=JQkJhXR9IMY">Watch on YouTube</a></li>
<li><strong>Architecture</strong>: MAUI pages with Canvas components</li>
<li><strong>Features</strong>: Vehicle telemetry, maps, data visualization, custom controls</li>
</ul>
<h2 id="contributing-samples">Contributing Samples</h2>
<p>Have a cool UI or feature? Submit a PR or open an issue to share your sample with the community!</p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/samples.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
