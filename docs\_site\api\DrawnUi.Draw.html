<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Namespace DrawnUi.Draw | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Namespace DrawnUi.Draw | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw">

  <h1 id="DrawnUi_Draw" data-uid="DrawnUi.Draw" class="text-break">Namespace DrawnUi.Draw</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>

    <h3 id="classes">
Classes
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ActionOnTickAnimator.html">ActionOnTickAnimator</a></dt>
      <dd><p>Just register this animator to run custom code on every frame creating a kind of game loop if needed.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.AddGestures.html">AddGestures</a></dt>
      <dd><p>For fast and lazy gestures handling to attach to dran controls inside the canvas only</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.AddGestures.GestureListener.html">AddGestures.GestureListener</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.AdjustBrightnessEffect.html">AdjustBrightnessEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.AdjustRGBEffect.html">AdjustRGBEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.AnimateExtensions.html">AnimateExtensions</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.AnimatorBase.html">AnimatorBase</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.BaseChainedEffect.html">BaseChainedEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.BaseColorFilterEffect.html">BaseColorFilterEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.BaseImageFilterEffect.html">BaseImageFilterEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.BindToParentContextExtension.html">BindToParentContextExtension</a></dt>
      <dd><p>Compiled-bindings-friendly implementation for &quot;Source.Parent.BindingContext.Path&quot;</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.BindablePropertyExtension.html">BindablePropertyExtension</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.BlinkAnimator.html">BlinkAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.BlurEffect.html">BlurEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.CachedGradient.html">CachedGradient</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.CachedObject.html">CachedObject</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.CachedShader.html">CachedShader</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.CachedShadow.html">CachedShadow</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.CellWIthHeight.html">CellWIthHeight</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ChainAdjustBrightnessEffect.html">ChainAdjustBrightnessEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ChainAdjustContrastEffect.html">ChainAdjustContrastEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ChainAdjustLightnessEffect.html">ChainAdjustLightnessEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ChainAdjustRGBEffect.html">ChainAdjustRGBEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ChainColorPresetEffect.html">ChainColorPresetEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ChainDropShadowsEffect.html">ChainDropShadowsEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ChainSaturationEffect.html">ChainSaturationEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ChainTintWithAlphaEffect.html">ChainTintWithAlphaEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ColorBlendAnimator.html">ColorBlendAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ColorExtensions.html">ColorExtensions</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ColorPresetEffect.html">ColorPresetEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ContainsPointResult.html">ContainsPointResult</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ContentLayout.html">ContentLayout</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ContrastEffect.html">ContrastEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ControlInStack.html">ControlInStack</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ControlsTracker.html">ControlsTracker</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.CriticallyDampedSpringTimingParameters.html">CriticallyDampedSpringTimingParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DataContextIterator.html">DataContextIterator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DebugImage.html">DebugImage</a></dt>
      <dd><p>Control for displaying used Surface as a preview image, for debugging purposes.
Do not use this in prod, this will be invalidated every frame, causing non-stop screen update.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DecelerationTimingParameters.html">DecelerationTimingParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DecelerationTimingVectorParameters.html">DecelerationTimingVectorParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DescendingZIndexGestureListenerComparer.html">DescendingZIndexGestureListenerComparer</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DrawingRect.html">DrawingRect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DrawnExtensions.html">DrawnExtensions</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DrawnFontAttributesConverter.html">DrawnFontAttributesConverter</a></dt>
      <dd><p>Forked from Microsoft.Maui.Controls as using original class was breaking XAML HotReload for some unknown reason</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DrawnUiStartupSettings.html">DrawnUiStartupSettings</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DropShadowEffect.html">DropShadowEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html">DynamicGrid&lt;T&gt;</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.EdgeGlowAnimator.html">EdgeGlowAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ElementRenderer.html">ElementRenderer</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.FindTagExtension.html">FindTagExtension</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.FluentExtensions.html">FluentExtensions</a></dt>
      <dd><p>Provides extension methods for fluent API design pattern with DrawnUI controls</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.FrameTimeInterpolator.html">FrameTimeInterpolator</a></dt>
      <dd><p>Interpolated time between frames, works in seconds. See examples..</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.InfiniteLayout.html">InfiniteLayout</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.KeyboardManager.html">KeyboardManager</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.LayoutStructure.html">LayoutStructure</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.LoadedImageSource.html">LoadedImageSource</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.Looper.html">Looper</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.LottieRefreshIndicator.html">LottieRefreshIndicator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.MauiKeyMapper.html">MauiKeyMapper</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.MeasuredListCell.html">MeasuredListCell</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.MeasuredListCells.html">MeasuredListCells</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ObservableAttachedItemsCollection-1.html">ObservableAttachedItemsCollection&lt;T&gt;</a></dt>
      <dd><p>We have to subclass ObservableCollection to avoid it sending empty oldItems upon Reset.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.PendulumAnimator.html">PendulumAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.PerpetualPendulumAnimator.html">PerpetualPendulumAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.PingPongAnimator.html">PingPongAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.Plane.html">Plane</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.PlanesScroll.html">PlanesScroll</a></dt>
      <dd><p>Provides the ability to create/draw views directly while scrolling.
Content will be generated dynamically, instead of the usual way.
This control main logic is inside PaintOnPlane override, also it hacks content to work without a real Content.
You have to override <code>GetMeasuredView</code> to provide your views to be drawn upon passed index.
TODO: for horizonal</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.PointIsInsideResult.html">PointIsInsideResult</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ProgressAnimator.html">ProgressAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ProgressTrail.html">ProgressTrail</a></dt>
      <dd><p>Progress trail component for linear progress bars.
Similar to SliderTrail but optimized for progress display.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RangeAnimator.html">RangeAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RangeVectorAnimator.html">RangeVectorAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RefreshIndicator.html">RefreshIndicator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RenderDrawingContext.html">RenderDrawingContext</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RenderLabel.html">RenderLabel</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RenderObject.html">RenderObject</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RenderTreeRenderer.html">RenderTreeRenderer</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RenderingAnimator.html">RenderingAnimator</a></dt>
      <dd><p>This animator renders on canvas instead of just updating a value</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RippleAnimator.html">RippleAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SaturationEffect.html">SaturationEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ScrollFlingAnimator.html">ScrollFlingAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ScrollFlingVectorAnimator.html">ScrollFlingVectorAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ShaderDoubleTexturesEffect.html">ShaderDoubleTexturesEffect</a></dt>
      <dd><p>Base shader effect class that has 2 input textures.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ShimmerAnimator.html">ShimmerAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.Sk3dView.html">Sk3dView</a></dt>
      <dd><p>Custom implementation of Android's Camera 3D helper for SkiaSharp</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkCamera3D.html">SkCamera3D</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkCamera3D2.html">SkCamera3D2</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkPatch3D.html">SkPatch3D</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaBackdrop.html">SkiaBackdrop</a></dt>
      <dd><p>Warning with CPU-rendering edges will not be blurred: <a href="https://issues.skia.org/issues/40036320">https://issues.skia.org/issues/40036320</a></p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaBevel.html">SkiaBevel</a></dt>
      <dd><p>Defines properties for creating bevel or emboss effects on shapes.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaButton.html">SkiaButton</a></dt>
      <dd><p>Button-like control, can include any content inside. It's either you use default content (todo templates?..)
or can include any content inside, and properties will by applied by convention to a SkiaLabel with Tag <code>MainLabel</code>, SkiaShape with Tag <code>MainFrame</code>. At the same time you can override ApplyProperties() and apply them to your content yourself.
Convention elements tags: BtnText, BtnShape.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaButton.ButtonLabel.html">SkiaButton.ButtonLabel</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaCheckbox.html">SkiaCheckbox</a></dt>
      <dd><p>Switch-like control, can include any content inside. It's aither you use default content (todo templates?..)
or can include any content inside, and properties will by applied by convention to a SkiaShape with Tag <code>Frame</code>, SkiaShape with Tag <code>Thumb</code>. At the same time you can override ApplyProperties() and apply them to your content yourself.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.html">SkiaControl.ControlTappedEventArgs</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaControlWithRect.html">SkiaControlWithRect</a></dt>
      <dd><p>Used inside RenderingTree. Rect is real drawing position</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaControlsObservable.html">SkiaControlsObservable</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaCursor.html">SkiaCursor</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaDoubleAttachedTexturesEffect.html">SkiaDoubleAttachedTexturesEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaDrawingContext.html">SkiaDrawingContext</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaEditor.html">SkiaEditor</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaEffect.html">SkiaEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaFontManager.html">SkiaFontManager</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaFrame.html">SkiaFrame</a></dt>
      <dd><p>Alias for SkiaShape type Rectangle</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaGesturesInfo.html">SkiaGesturesInfo</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaGesturesParameters.html">SkiaGesturesParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaGradient.html">SkiaGradient</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaGrid.html">SkiaGrid</a></dt>
      <dd><p>MAUI Grid alternative</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaHotspot.html">SkiaHotspot</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaHotspotZoom.html">SkiaHotspotZoom</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaHoverMask.html">SkiaHoverMask</a></dt>
      <dd><p>Paints the parent view with the background color with a clipped viewport oth this view size</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaImage.html">SkiaImage</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaImage.RescaledBitmap.html">SkiaImage.RescaledBitmap</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaImageEffects.html">SkiaImageEffects</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaImageManager.html">SkiaImageManager</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaImageManager.QueueItem.html">SkiaImageManager.QueueItem</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaImageTiles.html">SkiaImageTiles</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLabel.html">SkiaLabel</a></dt>
      <dd><p>A high-performance text rendering control that provides advanced text formatting,
layout, and styling capabilities using SkiaSharp for rendering.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLabel.DecomposedText.html">SkiaLabel.DecomposedText</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLabel.EmojiData.html">SkiaLabel.EmojiData</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLabel.ObjectPools.html">SkiaLabel.ObjectPools</a></dt>
      <dd><p>Thread-safe object pools for reducing GC allocations in text measurement</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLabel.SpanCollection.html">SkiaLabel.SpanCollection</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLabel.SpanMeasurement.html">SkiaLabel.SpanMeasurement</a></dt>
      <dd><p>Span-based measurement methods to avoid string allocations</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLabel.TextMetrics.html">SkiaLabel.TextMetrics</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLabelFps.html">SkiaLabelFps</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLayer.html">SkiaLayer</a></dt>
      <dd><p>Absolute layout like MAUI Grid with just one column and row</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLayout.html">SkiaLayout</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLayout.BuildWrapLayout.html">SkiaLayout.BuildWrapLayout</a></dt>
      <dd><p>Implementation for LayoutType.Wrap</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLayout.Cell.html">SkiaLayout.Cell</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLayout.SecondPassArrange.html">SkiaLayout.SecondPassArrange</a></dt>
      <dd><p>Cell.Area contains the area for layout</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.html">SkiaLayout.SkiaGridStructure</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaMauiElement.html">SkiaMauiElement</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaPoint.html">SkiaPoint</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaProgress.html">SkiaProgress</a></dt>
      <dd><p>Linear progress bar control with platform-specific styling.
Shows progress from Min to Value within the Min-Max range.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaRangeBase.html">SkiaRangeBase</a></dt>
      <dd><p>Base class for range-based controls like sliders and progress bars.
Provides common functionality for value ranges, track management, and platform styling.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaRichLabel.html">SkiaRichLabel</a></dt>
      <dd><p>Will internally create spans from markdown.
Spans property must not be set directly.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaRow.html">SkiaRow</a></dt>
      <dd><p>Horizontal stack,  like MAUI HorizontalStackLayout</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaScroll.html">SkiaScroll</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaScrollLooped.html">SkiaScrollLooped</a></dt>
      <dd><p>Cycles content, so the scroll never ands but cycles from the start</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaSetter.html">SkiaSetter</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html">SkiaShaderEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaShadow.html">SkiaShadow</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaShape.html">SkiaShape</a></dt>
      <dd><p>Extension of SkiaShape that adds bevel and emboss functionality</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaSlider.html">SkiaSlider</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaStack.html">SkiaStack</a></dt>
      <dd><p>Vertical stack, like MAUI VerticalStackLayout</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaSvg.html">SkiaSvg</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaSwitch.html">SkiaSwitch</a></dt>
      <dd><p>Switch-like control, can include any content inside. It's aither you use default content (todo templates?..)
or can include any content inside, and properties will by applied by convention to a SkiaShape with Tag <code>Frame</code>, SkiaShape with Tag <code>Thumb</code>. At the same time you can override ApplyProperties() and apply them to your content yourself.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaToggle.html">SkiaToggle</a></dt>
      <dd><p>Base control for toggling between 2 states.
It provides no gestures support by itsself.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html">SkiaValueAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaVectorAnimator.html">SkiaVectorAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaWrap.html">SkiaWrap</a></dt>
      <dd><p>A powerful flexible control, a bit like WPF StackPanel, arranges children in a responsive way according available size. Can change the number of Columns to use by default.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SliderThumb.html">SliderThumb</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SliderTrail.html">SliderTrail</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SliderValueDesc.html">SliderValueDesc</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.Snapping.html">Snapping</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SnappingLayout.html">SnappingLayout</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SortedGestureListeners.html">SortedGestureListeners</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SpringExtensions.html">SpringExtensions</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SpringTimingParameters.html">SpringTimingParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SpringTimingVectorParameters.html">SpringTimingVectorParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SpringWithVelocityAnimator.html">SpringWithVelocityAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SpringWithVelocityVectorAnimator.html">SpringWithVelocityVectorAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.StackLayoutStructure.html">StackLayoutStructure</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.StateEffect.html">StateEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html">StaticResourcesExtensions</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.Super.html">Super</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SvgSpan.html">SvgSpan</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.TemplatedViewsPool.html">TemplatedViewsPool</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.TextLine.html">TextLine</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.TintEffect.html">TintEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.TintWithAlphaEffect.html">TintWithAlphaEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ToggleAnimator.html">ToggleAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.TrackedObject-1.html">TrackedObject&lt;T&gt;</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.UnderdampedSpringTimingParameters.html">UnderdampedSpringTimingParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.VelocityAccumulator.html">VelocityAccumulator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ViewsAdapter.html">ViewsAdapter</a></dt>
      <dd><p>Top level class for working with ItemTemplates. Holds visible views.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ViewsIterator.html">ViewsIterator</a></dt>
      <dd><p>To iterate over virtual views</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.VirtualScroll.html">VirtualScroll</a></dt>
      <dd><p>this control gets a view and draws it on a virtual scrolling plane</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ViscousFluidInterpolator.html">ViscousFluidInterpolator</a></dt>
      <dd><p>Ported from google android</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.VisualLayer.html">VisualLayer</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.VisualTreeHandler.html">VisualTreeHandler</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ZoomContent.html">ZoomContent</a></dt>
      <dd><p>Wrapper to zoom and pan content by changing the rendering scale so not affecting quality, this is not a transform.TODO add animated movement</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ZoomEventArgs.html">ZoomEventArgs</a></dt>
      <dd></dd>
    </dl>
    <h3 id="structs">
Structs
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ApplySpan.html">ApplySpan</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ChainEffectResult.html">ChainEffectResult</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.CriticallyDampedSpringTimingVectorParameters.html">CriticallyDampedSpringTimingVectorParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.GestureEventProcessingInfo.html">GestureEventProcessingInfo</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.LineGlyph.html">LineGlyph</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.LineSpan.html">LineSpan</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.LinearInterpolationTimingParameters.html">LinearInterpolationTimingParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.MeasureRequest.html">MeasureRequest</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.PlanesScroll.ViewLayoutInfo.html">PlanesScroll.ViewLayoutInfo</a></dt>
      <dd><p>Holds layout information for a rendered month cell.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RangeF.html">RangeF</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ScaledPoint.html">ScaledPoint</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ScaledRect.html">ScaledRect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ScrollToIndexOrder.html">ScrollToIndexOrder</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ScrollToPointOrder.html">ScrollToPointOrder</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.ParentMeasureRequest.html">SkiaControl.ParentMeasureRequest</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaLabel.PooledStringBuilder.html">SkiaLabel.PooledStringBuilder</a></dt>
      <dd><p>Helper struct for managing pooled StringBuilder with automatic return</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaShape.ShapePaintArguments.html">SkiaShape.ShapePaintArguments</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.StringReference.html">StringReference</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.UnderdampedSpringTimingVectorParameters.html">UnderdampedSpringTimingVectorParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.UsedGlyph.html">UsedGlyph</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.WindowParameters.html">WindowParameters</a></dt>
      <dd></dd>
    </dl>
    <h3 id="interfaces">
Interfaces
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IAfterEffectDelete.html">IAfterEffectDelete</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IAnimatorsManager.html">IAnimatorsManager</a></dt>
      <dd><p>This control is responsible for updating screen for running animators</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IBindingContextDebuggable.html">IBindingContextDebuggable</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ICanBeUpdated.html">ICanBeUpdated</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ICanBeUpdatedWithContext.html">ICanBeUpdatedWithContext</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ICanRenderOnCanvas.html">ICanRenderOnCanvas</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IColorEffect.html">IColorEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IDampingTimingParameters.html">IDampingTimingParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IDampingTimingVectorParameters.html">IDampingTimingVectorParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IDefinesViewport.html">IDefinesViewport</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IDrawnBase.html">IDrawnBase</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IDrawnTextSpan.html">IDrawnTextSpan</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IHasAfterEffects.html">IHasAfterEffects</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IHasBanner.html">IHasBanner</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IImageEffect.html">IImageEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IInsideViewport.html">IInsideViewport</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IInsideWheelStack.html">IInsideWheelStack</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IInterpolator.html">IInterpolator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ILayoutInsideViewport.html">ILayoutInsideViewport</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IOverlayEffect.html">IOverlayEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IPostRendererEffect.html">IPostRendererEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IRefreshIndicator.html">IRefreshIndicator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IRenderEffect.html">IRenderEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IRenderObject.html">IRenderObject</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ISkiaAnimator.html">ISkiaAnimator</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ISkiaCell.html">ISkiaCell</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ISkiaDrawable.html">ISkiaDrawable</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ISkiaEffect.html">ISkiaEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ISkiaGestureListener.html">ISkiaGestureListener</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ISkiaGestureProcessor.html">ISkiaGestureProcessor</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ISkiaGridLayout.html">ISkiaGridLayout</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ISkiaLayer.html">ISkiaLayer</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ISkiaLayout.html">ISkiaLayout</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ISkiaSharpView.html">ISkiaSharpView</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IStateEffect.html">IStateEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ITimingParameters.html">ITimingParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ITimingVectorParameters.html">ITimingVectorParameters</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IVisibilityAware.html">IVisibilityAware</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.IWithContent.html">IWithContent</a></dt>
      <dd></dd>
    </dl>
    <h3 id="enums">
Enums
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.AutoSizeType.html">AutoSizeType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.BevelType.html">BevelType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ContextArguments.html">ContextArguments</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DirectionType.html">DirectionType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DrawImageAlignment.html">DrawImageAlignment</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.DrawTextAlignment.html">DrawTextAlignment</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.FontWeight.html">FontWeight</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.GesturesMode.html">GesturesMode</a></dt>
      <dd><p>Used by the canvas, do not need this for drawn controls</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.GlowPosition.html">GlowPosition</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.GradientType.html">GradientType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.LayoutType.html">LayoutType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.LinearDirectionType.html">LinearDirectionType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.LoadPriority.html">LoadPriority</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.LockTouch.html">LockTouch</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.MauiKey.html">MauiKey</a></dt>
      <dd><p>These are platform-independent. They correspond to JavaScript keys.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.MeasuringStrategy.html">MeasuringStrategy</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ObjectAliveType.html">ObjectAliveType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.OrientationType.html">OrientationType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.PanningModeType.html">PanningModeType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.PointedDirectionType.html">PointedDirectionType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.PrebuiltControlStyle.html">PrebuiltControlStyle</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RangeZone.html">RangeZone</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RecycleTemplateType.html">RecycleTemplateType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RecyclingTemplate.html">RecyclingTemplate</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RelativePositionType.html">RelativePositionType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.RenderingModeType.html">RenderingModeType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ScrollInteractionState.html">ScrollInteractionState</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ShapeType.html">ShapeType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SidePosition.html">SidePosition</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaAnchorBak.html">SkiaAnchorBak</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaButton.ButtonStyleType.html">SkiaButton.ButtonStyleType</a></dt>
      <dd><p>Defines the button style variants available for different visual appearances.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaButton.IconPositionType.html">SkiaButton.IconPositionType</a></dt>
      <dd><p>Defines the position of an icon relative to the button text</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaCacheType.html">SkiaCacheType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.CacheValidityType.html">SkiaControl.CacheValidityType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaImageEffect.html">SkiaImageEffect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SkiaTouchAnimation.html">SkiaTouchAnimation</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SnapToChildrenType.html">SnapToChildrenType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SourceType.html">SourceType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.SpaceDistribution.html">SpaceDistribution</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.TextTransform.html">TextTransform</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.TransformAspect.html">TransformAspect</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.ViewportScrollType.html">ViewportScrollType</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Draw.VirtualisationType.html">VirtualisationType</a></dt>
      <dd></dd>
    </dl>


</article>

        <div class="contribution d-print-none">
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
