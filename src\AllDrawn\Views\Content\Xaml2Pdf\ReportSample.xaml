<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="Sandbox.Views.Xaml2Pdf.ReportSample"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:strings1="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Resources.Strings"
    Tag="ReportWrapper">

    <draw:SkiaShape
        Margin="16"
        Padding="16"
        CornerRadius="8"
        StrokeColor="Black"
        StrokeWidth="1">

        <draw:SkiaLayout
            Spacing="12"
            Tag="ReportColumn"
            Type="Column">

            <draw:SkiaLabel
                Tag="ReportTitle"
                FontFamily="FontText"
                Text="This is a report sample, we will use different controls inside. We are using a localized Markdown text from app resources below:"
                TextColor="Black" />

            <draw:SkiaRichLabel
                BackgroundColor="Gainsboro"
                FontSize="13"
                LinkColor="Blue"
                Text="{x:Static strings1:ResStrings.MarkdownTest}"
                TextColor="Black" />

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                Type="Absolute">

                <draw:SkiaImage
                    LoadSourceOnFirstDraw="False"
                    Success="SkiaImage_OnSuccess"
                    Source="Pics/food.jpg"
                    WidthRequest="90" />

                <draw:SkiaDecoratedGrid
                    Margin="100,0,0,0"
                    ColumnDefinitions="1*,1*,1*,1*"
                    HorizontalOptions="Fill"
                    RowDefinitions="30,40"
                    Type="Grid">

                    <!--  header  -->
                    <draw:SkiaLabel
                        Grid.Column="0"
                        FontFamily="FontText"
                        HorizontalOptions="Center"
                        Text="WinUI"
                        TextColor="Black"
                        VerticalOptions="Center" />

                    <draw:SkiaLabel
                        Grid.Column="1"
                        FontFamily="FontText"
                        HorizontalOptions="Center"
                        Text="Android"
                        TextColor="Black"
                        VerticalOptions="Center" />

                    <draw:SkiaLabel
                        Grid.Column="2"
                        HorizontalOptions="Center"
                        Text="iOS"
                        TextColor="Black"
                        VerticalOptions="Center" />

                    <draw:SkiaLabel
                        Grid.Column="3"
                        HorizontalOptions="Center"
                        Text="MacCatalyst"
                        TextColor="Black"
                        VerticalOptions="Center" />

                    <!--  data  -->
                    <draw:SkiaLabel
                        Grid.Row="1"
                        Grid.Column="0"
                        FontFamily="FontText"
                        HorizontalOptions="Center"
                        Text="Yes"
                        TextColor="Green"
                        VerticalOptions="Center" />

                    <draw:SkiaLabel
                        Grid.Row="1"
                        Grid.Column="1"
                        FontFamily="FontText"
                        HorizontalOptions="Center"
                        Text="Yes"
                        TextColor="Green"
                        VerticalOptions="Center" />

                    <draw:SkiaLabel
                        Grid.Row="1"
                        Grid.Column="2"
                        HorizontalOptions="Center"
                        Text="Yes"
                        TextColor="Green"
                        VerticalOptions="Center" />

                    <draw:SkiaLabel
                        Grid.Row="1"
                        Grid.Column="3"
                        HorizontalOptions="Center"
                        Text="Yes"
                        TextColor="Green"
                        VerticalOptions="Center" />

                </draw:SkiaDecoratedGrid>

            </draw:SkiaLayout>

            <!--  Export data from viewmodel  -->
            <draw:SkiaLabel
                Text="{Binding BindableText}"
                TextColor="Red" />


        </draw:SkiaLayout>

    </draw:SkiaShape>

</draw:SkiaLayout>
