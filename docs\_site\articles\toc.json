{"items": [{"name": "Home", "href": "index.html", "topicHref": "index.html"}, {"name": "Getting Started", "items": [{"name": "Installation and Setup", "href": "getting-started.html", "topicHref": "getting-started.html"}, {"name": "Your First DrawnUi App", "href": "first-app.html", "topicHref": "first-app.html"}, {"name": "Interactive Card Gallery", "href": "interactive-cards.html", "topicHref": "interactive-cards.html"}, {"name": "News Feed Tutorial", "href": "news-feed-tutorial.html", "topicHref": "news-feed-tutorial.html"}, {"name": "Porting Native to Drawn", "href": "porting-maui.html", "topicHref": "porting-maui.html"}]}, {"name": "<PERSON><PERSON>", "href": "samples.html", "topicHref": "samples.html"}, {"name": "Controls", "href": "controls/index.html", "topicHref": "controls/index.html", "items": [{"name": "Controls Overview", "href": "controls/index.html", "topicHref": "controls/index.html"}, {"name": "Buttons", "href": "controls/buttons.html", "topicHref": "controls/buttons.html"}, {"name": "Switches and Toggles", "href": "controls/switches.html", "topicHref": "controls/switches.html"}, {"name": "Input Controls", "href": "controls/input.html", "topicHref": "controls/input.html"}, {"name": "Layout Controls", "href": "controls/layouts.html", "topicHref": "controls/layouts.html"}, {"name": "Scroll Views", "href": "controls/scroll.html", "topicHref": "controls/scroll.html"}, {"name": "Carousels", "href": "controls/carousels.html", "topicHref": "controls/carousels.html"}, {"name": "Drawers", "href": "controls/drawers.html", "topicHref": "controls/drawers.html"}, {"name": "Native Integration", "href": "controls/native-integration.html", "topicHref": "controls/native-integration.html"}, {"name": "<PERSON><PERSON><PERSON>", "href": "controls/shapes.html", "topicHref": "controls/shapes.html"}, {"name": "Text and Labels", "href": "controls/text.html", "topicHref": "controls/text.html"}, {"name": "Images", "href": "controls/images.html", "topicHref": "controls/images.html"}, {"name": "Sprites", "href": "controls/sprites.html", "topicHref": "controls/sprites.html"}, {"name": "Animations", "href": "controls/animations.html", "topicHref": "controls/animations.html"}, {"name": "Navigation Shell", "href": "controls/shell.html", "topicHref": "controls/shell.html"}]}, {"name": "Advanced", "href": "advanced/index.html", "topicHref": "advanced/index.html", "items": [{"name": "Drawing Pipeline", "href": "drawing-pipeline.html", "topicHref": "drawing-pipeline.html"}, {"name": "Fluent C# Extensions", "href": "fluent-extensions.html", "topicHref": "fluent-extensions.html"}, {"name": "Platform-Specific Styling", "href": "advanced/platform-styling.html", "topicHref": "advanced/platform-styling.html"}, {"name": "Layout System Architecture", "href": "advanced/layout-system.html", "topicHref": "advanced/layout-system.html"}, {"name": "Gradients", "href": "advanced/gradients.html", "topicHref": "advanced/gradients.html"}, {"name": "Game UI & Interactive Games", "href": "advanced/game-ui.html", "topicHref": "advanced/game-ui.html"}, {"name": "SkiaScroll & Virtualization", "href": "advanced/skiascroll.html", "topicHref": "advanced/skiascroll.html"}, {"name": "Gestures & Touch Input", "href": "advanced/gestures.html", "topicHref": "advanced/gestures.html"}]}, {"name": "FAQ", "href": "faq.html", "topicHref": "faq.html"}]}