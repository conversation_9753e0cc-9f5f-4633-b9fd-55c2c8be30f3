# 3-Plane Virtualization System in DrawnUI

## Overview

The 3-plane virtualization system is DrawnUi's advanced scrolling mechanism designed to achieve **smooth scrolling** for large datasets by using bitmap caching and background preparation instead of redrawing individual cells during scroll operations.

## 1. The Concept (Idea)

### Core Principle
Instead of redrawing every visible cell on each scroll frame, the system:
1. **Pre-renders content to bitmap planes** (off-screen surfaces)
2. **Translates these bitmaps** during scroll operations (GPU-accelerated)
3. **Prepares new planes in background** while current plane is visible
4. **Swaps planes** when scroll thresholds are reached

### The 3-Plane Architecture
- **Current Plane**: Currently visible content rendered as bitmap
- **Forward Plane**: Next content prepared for scrolling down/right
- **Backward Plane**: Previous content prepared for scrolling up/left

Each plane covers **2x viewport size** to ensure smooth transitions.

### Key Benefits
- **Smooth scrolling**: No per-frame cell redrawing
- **Memory efficient**: Only 3 planes in memory regardless of dataset size
- **Background preparation**: Next content ready before needed
- **GPU acceleration**: Bitmap translation is hardware-accelerated

## 2. Current State (Implementation)

### Activation Conditions
The system activates when **all** conditions are met:
```csharp
// SkiaScroll.UseVirtual property
return Content != null
       && Orientation != ScrollOrientation.Both 
       && Content is SkiaLayout layout 
       && layout.Virtualisation == VirtualisationType.Managed;
```

**Required Configuration:**
- SkiaScroll containing SkiaLayout
- `MeasureItemsStrategy="MeasureVisible"`
- `Virtualisation="Managed"`
- `IsTemplated=true` (ItemTemplate + ItemsSource)

### Current Implementation Flow

#### 1. Plane Initialization
```csharp
// Each plane is 2x viewport height for smooth transitions
_planeWidth = (int)(viewportWidth);
_planeHeight = (int)(viewportHeight * 2);
_planePrepareThreshold = (int)(_planeHeight / 2);
```

#### 2. Drawing Decision
```csharp
if (UseVirtual)
{
    DrawVirtual(context);  // Use 3-plane system
}
else
{
    PaintViews(context);   // Traditional drawing
}
```

#### 3. Plane Rendering
Each plane is rendered to an off-screen surface:
```csharp
protected virtual void PreparePlane(DrawingContext context, Plane plane)
{
    // Create recording context for the plane surface
    var recordingContext = context.CreateForRecordingImage(plane.Surface, destination.Size);
    
    // Render content to the plane
    PaintOnPlane(recordingContext, plane);
    
    // Cache as bitmap
    plane.CachedObject = new CachedObject(SkiaCacheType.Image, plane.Surface, ...);
    plane.IsReady = true;
}
```

#### 4. Bitmap Drawing
During scroll, planes are drawn as cached bitmaps:
```csharp
// Draw each plane as a bitmap if it intersects viewport
if (ContentViewport.Pixels.IntersectsWith(rectCurrent))
{
    PlaneCurrent.CachedObject.Draw(context.Context.Canvas, rectCurrent.Left, rectCurrent.Top, null);
}
```

#### 5. Background Preparation
Hidden planes are prepared asynchronously:
```csharp
Task.Run(async () =>
{
    await _planeLocks[planeId].WaitAsync(token);
    PreparePlane(clone.WithArgument(new("BThread", true)), plane);
}, token);
```

#### 6. Incremental Measurement
With `MeasureVisible` strategy, only visible items are measured initially:
```csharp
if (layout.MeasureItemsStrategy == MeasuringStrategy.MeasureVisible
    && layout.LastMeasuredIndex < layout.ItemsSource.Count)
{
    var measuredEnd = layout.GetMeasuredContentEnd();
    if (measuredEnd - currentOffset < 0)
    {
        TriggerIncrementalMeasurement(layout);
    }
}
```

### Critical Bugs Discovered & Fixed

#### 1. **Background Rendering Race Condition** 🚨 **[FIXED]**
**Root Cause:** Background `Task.Run` operations continued with stale plane offsets after planes were repositioned during scrolling.

**The Problem:**
- User scrolls DOWN → triggers background rendering for lower content at offset Y=-1000
- User quickly scrolls UP → plane gets repositioned to offset Y=-500 for upper content
- Old background task completes and renders content for Y=-1000 into plane positioned at Y=-500
- Result: Wrong content appears in plane

**Solution:** Architectural separation of concerns:
- **`OffsetY`**: Current plane position (changes during scrolling)
- **`ContentOffsetY`**: Immutable content offset captured at rendering start
- Background rendering uses `ContentOffsetY` to prevent race conditions

#### 2. **Plane Visibility/Invalidation Bug** 🚨 **[FIXED]**
**Root Cause:** When background plane preparation completed, the scroll container wasn't invalidated to trigger a redraw.

**The Problem:**
- Background plane preparation completes successfully
- Plane is marked as ready with correct content
- But scroll container doesn't know to repaint and show the newly prepared plane
- User sees empty/stale plane until next scroll event triggers invalidation

**Solution:** Added proper invalidation after plane preparation completes using `Repaint()`.

#### 3. **Missing Cancellation Infrastructure** 🚨 **[FIXED]**
**Root Cause:** `CancelBackgroundRenderingForPlane()` method existed but was **NEVER CALLED** where needed.

**The Problem:**
- Planes get repositioned during normal scrolling via `SwapDown()` and `SwapUp()`
- Old background rendering tasks continue for previous positions
- Multiple background tasks run simultaneously for same plane
- Results in race conditions and wrong content

**Solution:** Added missing cancellation calls:
- `SwapDown()` now cancels Forward plane background rendering
- `SwapUp()` now cancels Backward plane background rendering

#### 4. **Faulty Preparation Logic** 🚨 **[FIXED]**
**Root Cause:** Condition `|| ViewportOffsetY == 0` prevented Forward plane preparation exactly when needed most.

**The Problem:**
- At bottom of content, Forward plane should be prepared for overscroll bounce
- But condition `|| ViewportOffsetY == 0` blocked preparation
- GREEN plane only appeared after overscroll bounce, not automatically

**Solution:** Removed the faulty condition that was preventing Forward plane preparation at bottom.

#### 5. **Layout Configuration Bug** ⚠️ **[FIXED]**
**Root Cause:** VirtualizedLayout inside SkiaScroll had `VerticalOptions = LayoutOptions.Fill`

**The Problem:**
- Layout fills viewport instead of extending beyond it
- Prevents scrolling beyond viewport boundaries
- Critical for 3-plane system which needs content larger than viewport

**Solution:** Changed to `VerticalOptions = LayoutOptions.Start` to enable proper scrolling.

### Current Issues & Analysis

#### 6. **Reactive vs Proactive Plane Preparation** 🔄 **[IN PROGRESS]**
**Root Cause:** Planes are prepared only when they should be shown, not in advance.

**The Problem:**
- Current logic: Prepare Forward plane when scrolling down, Backward plane when scrolling up
- This is **REACTIVE** - waiting until scroll direction to prepare plane
- Results in visible delays when changing scroll direction
- User expectation: Planes should be prepared **FAR IN ADVANCE** since it's background work

**Current Solution Attempt:**
```csharp
// WRONG: Only prepare when scrolling in specific direction
if (scrollDirection == ScrollDirection.Down && !PlaneForward.IsReady)

// BETTER: Prepare all planes when ANY scrolling happens
if (!_planeBuildStates["Forward"].IsBuilding &&
    (!PlaneForward.IsReady || PlaneForward.ContentOffsetY != PlaneForward.OffsetY))
```

**Key Insight:** A plane can be "ready" but contain content for the **wrong offset position**. Need to check if content matches current position.

#### 7. **Remaining Architectural Issues**
- **Orientation Support**: Currently optimized for vertical scrolling only
- **Plane Management**: Complex swapping logic with edge cases
- **Integration Complexity**: Tightly coupled with SkiaLayout's MeasureVisible strategy
- **Configuration Requirements**: Requires specific property combinations to activate
- **Debugging & Monitoring**: Limited visibility into plane states and performance

## 3. Lessons Learned & What Went Wrong

### My Analysis Mistakes
1. **Oversimplified the Problem**: Initially thought it was just about "preparing planes in advance" without understanding the offset position complexity.

2. **Missed the Core Issue**: Planes can be "ready" but contain content for the wrong offset position. The real problem is **content staleness**, not just readiness.

3. **Reactive vs Proactive Confusion**: Focused on scroll direction logic instead of understanding that planes need re-preparation when their offset positions change.

4. **Incomplete Race Condition Understanding**: Took multiple iterations to realize that background rendering continues with stale offsets after plane repositioning.

### Key Insights for Solid Implementation
1. **Plane Content Validity**: Always check if plane content matches current offset position, not just if it's "ready"
2. **Offset Separation**: Separate plane positioning (`OffsetY`) from content calculation (`ContentOffsetY`) to prevent race conditions
3. **Proactive Preparation**: Prepare ALL planes when ANY scrolling happens, don't wait for specific directions
4. **Proper Cancellation**: Cancel background rendering when planes are repositioned during normal scrolling
5. **Invalidation is Critical**: Always invalidate after background plane preparation completes

## 4. Next Goals

### Immediate Priority
1. **Complete Proactive Preparation Fix**
   - Verify that planes are prepared when offset positions change
   - Test that content staleness detection works correctly
   - Ensure smooth scrolling in both directions

2. **Performance Validation**
   - Measure actual scrolling performance improvements
   - Verify memory usage is reasonable
   - Test with large datasets

### Long-term Architecture Goals
1. **Robust Plane Management**
   - Centralized plane preparation logic (✅ Completed)
   - Better offset position tracking and validation
   - Improved cancellation and synchronization

2. **Enhanced Debugging**
   - Visual plane indicators with color coding (✅ Completed)
   - Performance metrics and diagnostics
   - Better error handling and logging

3. **Generic Implementation**
   - Decouple from SkiaLayout dependency
   - Support multiple content types
   - Configurable plane count and sizing

## Implementation Files & Key Changes

### Core Files Modified
- **`src/Maui/DrawnUi/Draw/Scroll/Plane.cs`** - Added `ContentOffsetY/X` properties for race condition fix
- **`src/Maui/DrawnUi/Draw/Scroll/SkiaScroll.Planes.cs`** - Major refactoring with centralized plane preparation logic
- **`src/Maui/Samples/FastRepro/ThreePlaneVirtualizationTest.cs`** - Test implementation for debugging

### Critical Methods & Changes

#### Race Condition Prevention (Plane.cs)
```csharp
// NEW: Immutable content offsets captured at rendering start
public float ContentOffsetY { get; set; }
public float ContentOffsetX { get; set; }

public void CaptureContentOffset()
{
    ContentOffsetY = OffsetY;  // Capture current position for rendering
    ContentOffsetX = OffsetX;
}
```

#### Centralized Plane Preparation (SkiaScroll.Planes.cs)
```csharp
// NEW: Single method for all plane preparation decisions
private void DecideAndPreparePlanes(DrawingContext context, ScrollDirection scrollDirection, bool isAtTop, bool isAtBottom)
{
    // Check if plane content matches current offset position
    if (!_planeBuildStates["Forward"].IsBuilding &&
        PlaneForward != null &&
        (!PlaneForward.IsReady || PlaneForward.ContentOffsetY != PlaneForward.OffsetY))
    {
        // Prepare plane for current offset position
    }
}
```

#### Missing Cancellation Calls Added
```csharp
// FIXED: Added missing cancellation in plane swapping
private void SwapDown()
{
    CancelBackgroundRenderingForPlane("Forward");  // NEW: Cancel stale rendering
    // ... existing swap logic
}

private void SwapUp()
{
    CancelBackgroundRenderingForPlane("Backward"); // NEW: Cancel stale rendering
    // ... existing swap logic
}
```

### Debug Color Reference
- 🔴 **RED Plane (Current)** = Currently visible content
- 🟢 **GREEN Plane (Forward)** = Content for scrolling down/right
- 🔵 **BLUE Plane (Backward)** = Content for scrolling up/left

### Testing & Validation
Use `ThreePlaneVirtualizationTest.cs` to verify:
1. Planes appear immediately when scrolling starts (proactive preparation)
2. No empty planes or visible loading delays
3. Smooth transitions between planes
4. Proper color coding in debug output

This document captures our current understanding and serves as the foundation for continued 3-plane virtualization improvements.
