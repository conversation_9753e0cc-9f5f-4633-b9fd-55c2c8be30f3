# 3-Plane Virtualization Test

This test page (`ThreePlaneVirtualizationTest.cs`) is designed to test and demonstrate the 3-plane virtualization system in DrawnUi's SkiaScroll + SkiaLayout combination.

## Purpose

The test validates the execution flow documented in `docs/articles/3-plane-virtualization.md` and provides a practical environment for:

1. **Testing the 3-plane system activation** - Verifies that the correct configuration triggers `UseVirtual = true`
2. **Monitoring virtualization behavior** - Real-time display of plane states, measurement progress, and visible indices
3. **Stress testing** - Rapid data changes and scrolling to test plane swapping and background preparation
4. **Performance validation** - Smooth scrolling with large datasets using bitmap caching

## Configuration

The test uses the exact configuration required for 3-plane virtualization:

```csharp
VirtualizedLayout = new SkiaLayout
{
    // KEY CONFIGURATION FOR 3-PLANE VIRTUALIZATION
    MeasureItemsStrategy = MeasuringStrategy.MeasureVisible,
    Virtualisation = VirtualisationType.Managed,
    RecyclingTemplate = RecyclingTemplate.Enabled,
    ItemsSource = ItemsSource
};

MainScroll = new SkiaScroll
{
    Orientation = ScrollOrientation.Vertical,
    Content = VirtualizedLayout  // This triggers UseVirtual when conditions are met
};
```

## Test Features

### Basic Controls
- **Add 25 Items** - Adds test data to verify incremental measurement
- **Clear All** - Resets the dataset
- **Scroll Top/Bottom** - Tests plane preparation at boundaries

### Advanced Testing
- **Stress Test** - Rapidly adds items (10 every 100ms) with random scrolling to test:
  - Plane swapping under load
  - Background preparation efficiency
  - Memory management
  - Fast scrolling edge cases

- **Auto Scroll** - Continuous smooth scrolling to test:
  - Bitmap caching effectiveness
  - Plane interchange timing
  - Rendering performance

### Monitoring Display

The test provides real-time monitoring of:

1. **Status Line**: `Items: X | UseVirtual: true/false | Strategy: MeasureVisible | Type: Managed`
2. **Plane Info**: `3-Plane System Active | Measured: X/Y | Visible: start-end`

## Expected Behavior

### When 3-Plane System is Active (`UseVirtual = true`):
- Smooth scrolling without per-frame cell redrawing
- Background preparation of forward/backward planes
- Efficient handling of large datasets (1000+ items)
- Consistent frame rates during scrolling

### When 3-Plane System is Inactive:
- Traditional drawing path (`PaintViews` instead of `DrawVirtual`)
- May show performance degradation with large datasets
- Status will show "3-Plane System: Not Active"

## Navigation

From the main FastRepro page, tap "3-Plane Virtualization Test" to access this test.

## Key Implementation Details

The test demonstrates:

1. **Activation Conditions**: All required properties set correctly
2. **Data Template**: Complex cell layout with variable heights
3. **Incremental Measurement**: MeasureVisible strategy in action
4. **Plane Monitoring**: Access to virtualization state information
5. **Performance Testing**: Stress scenarios for validation

## Troubleshooting

If "3-Plane System: Not Active" appears:
1. Verify `MeasureItemsStrategy = MeasureVisible`
2. Verify `Virtualisation = Managed`
3. Ensure `ItemsSource` and `ItemTemplate` are set
4. Check that `ScrollOrientation != Both`

## Related Documentation

- `docs/articles/3-plane-virtualization.md` - Complete system documentation
- `src/Maui/DrawnUi/Draw/Scroll/SkiaScroll.Planes.cs` - Core implementation
- `src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs` - MeasureVisible logic

## 🐛 IDENTIFIED BUG: Forward Plane Not Rendering at Bottom

### Issue Description
When scrolling to the bottom of content, the forward plane (green) fails to render properly. The plane appears to be prepared but doesn't show content, leaving a gap or showing stale content.

### Root Cause Analysis

#### 1. **Plane Swapping Logic Issue**
In `SwapDown()` method (line 632-648 in SkiaScroll.Planes.cs):
```csharp
private void SwapDown()
{
    // forward ↑ current
    // current ↑ backward
    // backward ↓ forward + invalidate
    var temp = PlaneBackward;
    PlaneBackward = PlaneCurrent;
    PlaneCurrent = PlaneForward;
    PlaneForward = temp;

    // CRITICAL FIX: Cancel background rendering for plane being repositioned
    CancelBackgroundRenderingForPlane("Forward");

    PlaneForward.OffsetY = PlaneCurrent.OffsetY + _planeHeight;
    PlaneBackward.OffsetY = PlaneCurrent.OffsetY - _planeHeight;
    PlaneForward.Invalidate();
}
```

**PROBLEM**: When swapping down at bottom, the new forward plane gets positioned at `PlaneCurrent.OffsetY + _planeHeight`, which may be **beyond the content bounds**. This causes the plane to be prepared for content that doesn't exist.

#### 2. **Content Bounds Validation Missing**
The plane preparation logic doesn't validate if the requested content position is within valid content bounds. When at bottom:
- Content height might be 2000px
- Viewport height is 800px
- Current plane shows content at Y: 1200-2000px
- Forward plane tries to show content at Y: 2000-2800px ← **INVALID RANGE**

#### 3. **Race Condition in Background Rendering**
The `TriggerPreparePlane` method (line 210-278) captures content offset but doesn't validate bounds:
```csharp
// ARCHITECTURAL FIX: Capture content offset BEFORE starting background rendering
var plane = GetPlaneById(planeId);
plane?.CaptureContentOffset();  // Captures invalid Y position
```

#### 4. **Drawing Logic Flaw**
In `DrawVirtual` method (line 560-572), forward plane drawing:
```csharp
// Draw Forward
if (PlaneForward.IsReady)
{
    if (ContentViewport.Pixels.IntersectsWith(rectForward))
    {
        PlaneForward.CachedObject.Draw(context.Context.Canvas, rectForward.Left, rectForward.Top, null);
        PlaneForward.LastDrawnAt = rectForward;
    }
}
else
{
    PlaneForward.CachedObject?.Draw(context.Context.Canvas, rectForward.Left, rectForward.Top, null); //repeat last image for fast scrolling
}
```

**PROBLEM**: Even if `PlaneForward.IsReady = true`, the plane might contain empty/invalid content because it was prepared for out-of-bounds content area.

### Proposed Fix Strategy

#### 1. **Add Content Bounds Validation**
Leverage existing `ContentOffsetBounds` and `OverscrollDistance` infrastructure:
```csharp
private bool IsPlanePositionValid(float planeOffsetY, float planeHeight)
{
    // Use existing ContentOffsetBounds which already accounts for viewport
    var contentBounds = GetContentOffsetBounds(); // Returns scroll bounds, not content bounds
    var actualContentHeight = ContentSize.Pixels.Height;

    // Check if plane would show any actual content
    var planeTop = Math.Abs(planeOffsetY); // Convert scroll offset to content position
    var planeBottom = planeTop + planeHeight;

    return planeTop < actualContentHeight; // At least some content exists in this range
}
```

#### 2. **Modify SwapDown Logic**
Prevent forward plane positioning beyond content bounds using existing infrastructure:
```csharp
private void SwapDown()
{
    // ... existing swap logic ...

    var proposedForwardY = PlaneCurrent.OffsetY + _planeHeight;

    // Use existing bounds checking infrastructure
    if (!IsPlanePositionValid(proposedForwardY, _planeHeight))
    {
        Debug.WriteLine("Forward plane would be out of content bounds - disabling");
        PlaneForward.OffsetY = proposedForwardY; // Keep position for consistency
        PlaneForward.IsReady = false; // Mark as not ready to prevent drawing invalid content
        return; // Skip invalidation since plane won't be used
    }

    PlaneForward.OffsetY = proposedForwardY;
    PlaneBackward.OffsetY = PlaneCurrent.OffsetY - _planeHeight;
    PlaneForward.Invalidate();
}
```

#### 3. **Enhanced Plane Preparation Logic**
Add bounds checking in `CheckAndPreparePlane` using existing validation:
```csharp
private void CheckAndPreparePlane(DrawingContext context, string planeId, Plane plane)
{
    if (plane == null || _planeBuildStates[planeId].IsBuilding)
        return;

    var planeColor = GetPlaneColorName(planeId);

    // NEW: Validate content bounds before preparation
    if (!IsPlanePositionValid(plane.OffsetY, _planeHeight))
    {
        Debug.WriteLine($"{planeColor} Skipping {planeId} preparation - out of content bounds");
        plane.IsReady = false; // Mark as not ready to prevent drawing
        return;
    }

    // ... existing preparation logic ...
}
```

#### 4. **Alternative: Overscroll Plane Content**
Instead of disabling the plane, show overscroll content:
```csharp
private void PrepareOverscrollPlane(DrawingContext context, Plane plane)
{
    // Clear plane with background color or show overscroll indicator
    var recordingContext = context.CreateForRecordingImage(plane.Surface, plane.Destination.Size);
    recordingContext.Context.Canvas.Clear(plane.BackgroundColor);

    // Optional: Draw overscroll indicator (gradient, pattern, etc.)
    // This maintains smooth scrolling experience at boundaries

    plane.CachedObject = recordingContext.RecordedObject;
    plane.IsReady = true;
}
```

### Testing Steps to Reproduce
1. Load test with 50+ items
2. Scroll to absolute bottom using "Scroll Bottom" button
3. Observe forward plane (green border) - should be empty/missing
4. Try manual scroll down - forward plane doesn't appear
5. **Debug Output**: Look for plane preparation messages in debug console

### Expected Behavior After Fix
- Forward plane should either show valid content or be properly disabled at bottom
- No empty/stale content should appear in forward plane
- Smooth scrolling should continue working at content boundaries
- Debug output should show "out of content bounds" messages when appropriate

### Implementation Priority
**RECOMMENDED APPROACH**: Implement option #3 (Enhanced Plane Preparation Logic) first as it's the safest and most surgical fix. This prevents the root cause without changing core swapping logic.

### Key Files to Modify
1. **`src/Maui/DrawnUi/Draw/Scroll/SkiaScroll.Planes.cs`**:
   - Add `IsPlanePositionValid()` method
   - Modify `CheckAndPreparePlane()` to validate bounds
   - Optional: Modify `SwapDown()` and `SwapUp()` for additional safety

2. **Testing**: Use this test page to validate the fix works correctly

### Debug Monitoring
The test page already provides real-time monitoring of:
- Plane states (IsReady status)
- Content bounds (Measured vs Total items)
- Scroll position relative to content

Watch for:
- Forward plane `IsReady = false` when at bottom
- No stale content rendering in forward plane area
- Smooth scrolling maintained at boundaries

### Related Infrastructure
The fix leverages existing DrawnUi infrastructure:
- `ContentSize.Pixels.Height` - Actual content dimensions
- `GetContentOffsetBounds()` - Scroll boundary calculations
- `OverscrollDistance` - Overscroll detection
- `ViewportOffsetY` - Current scroll position
- Plane invalidation system - Existing cache management
