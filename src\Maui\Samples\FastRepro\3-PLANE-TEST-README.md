# 3-Plane Virtualization Test

This test page (`ThreePlaneVirtualizationTest.cs`) is designed to test and demonstrate the 3-plane virtualization system in DrawnUi's SkiaScroll + SkiaLayout combination.

## Purpose

The test validates the execution flow documented in `docs/articles/3-plane-virtualization.md` and provides a practical environment for:

1. **Testing the 3-plane system activation** - Verifies that the correct configuration triggers `UseVirtual = true`
2. **Monitoring virtualization behavior** - Real-time display of plane states, measurement progress, and visible indices
3. **Stress testing** - Rapid data changes and scrolling to test plane swapping and background preparation
4. **Performance validation** - Smooth scrolling with large datasets using bitmap caching

## Configuration

The test uses the exact configuration required for 3-plane virtualization:

```csharp
VirtualizedLayout = new SkiaLayout
{
    // KEY CONFIGURATION FOR 3-PLANE VIRTUALIZATION
    MeasureItemsStrategy = MeasuringStrategy.MeasureVisible,
    Virtualisation = VirtualisationType.Managed,
    RecyclingTemplate = RecyclingTemplate.Enabled,
    ItemsSource = ItemsSource
};

MainScroll = new SkiaScroll
{
    Orientation = ScrollOrientation.Vertical,
    Content = VirtualizedLayout  // This triggers UseVirtual when conditions are met
};
```

## Test Features

### Basic Controls
- **Add 25 Items** - Adds test data to verify incremental measurement
- **Clear All** - Resets the dataset
- **Scroll Top/Bottom** - Tests plane preparation at boundaries

### Advanced Testing
- **Stress Test** - Rapidly adds items (10 every 100ms) with random scrolling to test:
  - Plane swapping under load
  - Background preparation efficiency
  - Memory management
  - Fast scrolling edge cases

- **Auto Scroll** - Continuous smooth scrolling to test:
  - Bitmap caching effectiveness
  - Plane interchange timing
  - Rendering performance

### Monitoring Display

The test provides real-time monitoring of:

1. **Status Line**: `Items: X | UseVirtual: true/false | Strategy: MeasureVisible | Type: Managed`
2. **Plane Info**: `3-Plane System Active | Measured: X/Y | Visible: start-end`

## Expected Behavior

### When 3-Plane System is Active (`UseVirtual = true`):
- Smooth scrolling without per-frame cell redrawing
- Background preparation of forward/backward planes
- Efficient handling of large datasets (1000+ items)
- Consistent frame rates during scrolling

### When 3-Plane System is Inactive:
- Traditional drawing path (`PaintViews` instead of `DrawVirtual`)
- May show performance degradation with large datasets
- Status will show "3-Plane System: Not Active"

## Navigation

From the main FastRepro page, tap "3-Plane Virtualization Test" to access this test.

## Key Implementation Details

The test demonstrates:

1. **Activation Conditions**: All required properties set correctly
2. **Data Template**: Complex cell layout with variable heights
3. **Incremental Measurement**: MeasureVisible strategy in action
4. **Plane Monitoring**: Access to virtualization state information
5. **Performance Testing**: Stress scenarios for validation

## Troubleshooting

If "3-Plane System: Not Active" appears:
1. Verify `MeasureItemsStrategy = MeasureVisible`
2. Verify `Virtualisation = Managed`
3. Ensure `ItemsSource` and `ItemTemplate` are set
4. Check that `ScrollOrientation != Both`

## Related Documentation

- `docs/articles/3-plane-virtualization.md` - Complete system documentation
- `src/Maui/DrawnUi/Draw/Scroll/SkiaScroll.Planes.cs` - Core implementation
- `src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs` - MeasureVisible logic

## 🐛 ROOT CAUSE FOUND: Forward Plane Positioned Above Viewport

### Issue Description
The forward plane is being **drawn in the wrong position** - it's drawn above the visible viewport instead of below the current content. Debug output shows:

```
🟢 Draw Forward: rectForward={Left=0,Top=-161,Width=534,Height=688}
ContentViewport={Left=0,Top=268,Width=534,Height=344}
```

**PROBLEM**: Forward plane at `Top=-161` but viewport at `Top=268` - the plane is 429 pixels above where it should be visible!

### Root Cause: Plane Positioning Logic
The issue is in the `DrawVirtual` method plane positioning calculation:

```csharp
// currentScroll = -1805 (negative when scrolled down)
rectForward.Offset(0, currentScroll + PlaneForward.OffsetY);
```

When scrolled down, `currentScroll` is negative (-1805), and this negative value is being added to the plane's `OffsetY`, causing the forward plane to be positioned above the viewport instead of below the current content.

### Root Cause Analysis

#### 1. **Plane Swapping Logic Issue**
In `SwapDown()` method (line 632-648 in SkiaScroll.Planes.cs):
```csharp
private void SwapDown()
{
    // forward ↑ current
    // current ↑ backward
    // backward ↓ forward + invalidate
    var temp = PlaneBackward;
    PlaneBackward = PlaneCurrent;
    PlaneCurrent = PlaneForward;
    PlaneForward = temp;

    // CRITICAL FIX: Cancel background rendering for plane being repositioned
    CancelBackgroundRenderingForPlane("Forward");

    PlaneForward.OffsetY = PlaneCurrent.OffsetY + _planeHeight;
    PlaneBackward.OffsetY = PlaneCurrent.OffsetY - _planeHeight;
    PlaneForward.Invalidate();
}
```

**PROBLEM**: This positioning logic is correct, but the issue is that the forward plane is **not being drawn at all** when at bottom, suggesting a problem in the drawing/intersection logic.

#### 2. **Drawing Intersection Logic Issue**
In `DrawVirtual` method (line 560-572), forward plane drawing:
```csharp
// Draw Forward
if (PlaneForward.IsReady)
{
    if (ContentViewport.Pixels.IntersectsWith(rectForward))  // ← POTENTIAL ISSUE
    {
        PlaneForward.CachedObject.Draw(context.Context.Canvas, rectForward.Left, rectForward.Top, null);
        PlaneForward.LastDrawnAt = rectForward;
    }
}
```

**PROBLEM**: The `ContentViewport.Pixels.IntersectsWith(rectForward)` check may be failing when at bottom, preventing the forward plane from being drawn even if it's ready and positioned correctly.

#### 3. **Plane Preparation Not Triggered**
The `EvaluateAndPreparePlanesAsNeeded` method may not be triggering forward plane preparation when at bottom:
```csharp
// Calculate if we're at bottom: ViewportOffsetY is negative when scrolled down
var maxScrollDown = -(ContentSize.Pixels.Height - Viewport.Pixels.Height);
var isAtBottom = ViewportOffsetY <= maxScrollDown + 50; // Add 50px buffer for early preparation
```

**PROBLEM**: The `isAtBottom` calculation or the `ShouldPrepareForwardPlane` logic may not be working correctly.

#### 4. **Plane State Issues**
The forward plane may be:
- Not marked as `IsReady = true`
- Not being prepared at all when at bottom
- Being prepared but failing the intersection test for drawing

### Proposed Fix Strategy

#### 1. **Debug the Drawing Intersection Logic**
First, add debug output to understand why the forward plane isn't being drawn:
```csharp
// In DrawVirtual method, add debug output:
Debug.WriteLine($"Forward plane: IsReady={PlaneForward.IsReady}, rectForward={rectForward}");
Debug.WriteLine($"ContentViewport={ContentViewport.Pixels}");
Debug.WriteLine($"Intersects={ContentViewport.Pixels.IntersectsWith(rectForward)}");

// Draw Forward
if (PlaneForward.IsReady)
{
    if (ContentViewport.Pixels.IntersectsWith(rectForward))
    {
        Debug.WriteLine("Drawing forward plane");
        PlaneForward.CachedObject.Draw(context.Context.Canvas, rectForward.Left, rectForward.Top, null);
        PlaneForward.LastDrawnAt = rectForward;
    }
    else
    {
        Debug.WriteLine("Forward plane ready but no intersection - not drawing");
    }
}
else
{
    Debug.WriteLine("Forward plane not ready");
}
```

#### 2. **Debug Plane Preparation**
Add debug output to understand if the forward plane is being prepared:
```csharp
// In EvaluateAndPreparePlanesAsNeeded method:
Debug.WriteLine($"At bottom: {isAtBottom}, ScrollDirection: {scrollDirection}");
Debug.WriteLine($"Should prepare forward: {ShouldPrepareForwardPlane(isAtBottom, scrollDirection)}");

// In CheckAndPreparePlane method:
Debug.WriteLine($"Checking {planeId} plane: IsReady={plane.IsReady}, IsBuilding={_planeBuildStates[planeId].IsBuilding}");
```

#### 3. **Fix Intersection Logic**
The intersection test may be too strict. Try forcing the forward plane to draw:
```csharp
// Draw Forward - TEMPORARY DEBUG VERSION
if (PlaneForward.IsReady)
{
    // Force draw for debugging - remove intersection check temporarily
    Debug.WriteLine("Force drawing forward plane for debugging");
    PlaneForward.CachedObject.Draw(context.Context.Canvas, rectForward.Left, rectForward.Top, null);
    PlaneForward.LastDrawnAt = rectForward;
}
```

#### 4. **Check Plane Positioning**
Verify that plane rectangles are calculated correctly:
```csharp
// In DrawVirtual method, add position debugging:
Debug.WriteLine($"Plane positions at scroll {currentScroll}:");
Debug.WriteLine($"  Current: {rectCurrent}");
Debug.WriteLine($"  Forward: {rectForward}");
Debug.WriteLine($"  Backward: {rectBackward}");
Debug.WriteLine($"  Viewport: {ContentViewport.Pixels}");
```

### Testing Steps to Reproduce
1. Load test with 50+ items
2. Scroll to absolute bottom using "Scroll Bottom" button
3. **OBSERVE**: No green plane visible anywhere (should be visible below current content)
4. Try manual scroll down - forward plane completely missing
5. **Debug Output**: Check console for plane preparation and drawing messages

### Expected Behavior After Fix
- Forward plane (green border) should be visible below the current content area
- Forward plane should show appropriate content or overscroll indicator
- Smooth scrolling should continue working at content boundaries
- All three planes should be visible in their respective positions

### Implementation Priority
**DEBUGGING ADDED**: Comprehensive debugging messages have been added to track:
- 🎯 When decisions are made to prepare planes
- 🚀 When background preparation starts
- 🚫 When background preparation is canceled
- ✅ When background preparation completes
- 🟢 When forward plane drawing is attempted and why it succeeds/fails

**NEXT STEP**: Run the test and check debug output to understand the exact failure point.

### Key Files to Modify
1. **`src/Maui/DrawnUi/Draw/Scroll/SkiaScroll.Planes.cs`**:
   - Add `IsPlanePositionValid()` method
   - Modify `CheckAndPreparePlane()` to validate bounds
   - Optional: Modify `SwapDown()` and `SwapUp()` for additional safety

2. **Testing**: Use this test page to validate the fix works correctly

### Debug Monitoring
The test page already provides real-time monitoring of:
- Plane states (IsReady status)
- Content bounds (Measured vs Total items)
- Scroll position relative to content

Watch for:
- Forward plane `IsReady = false` when at bottom
- No stale content rendering in forward plane area
- Smooth scrolling maintained at boundaries

### Related Infrastructure
The fix leverages existing DrawnUi infrastructure:
- `ContentSize.Pixels.Height` - Actual content dimensions
- `GetContentOffsetBounds()` - Scroll boundary calculations
- `OverscrollDistance` - Overscroll detection
- `ViewportOffsetY` - Current scroll position
- Plane invalidation system - Existing cache management
