﻿namespace DrawnUi.Draw
{
    [DebuggerDisplay("{ToString(),nq}")]
    public class Plane
    {
        public override string ToString()
        {
            return $"Plane {Id}, offset {OffsetX},{OffsetY}, destination {Destination}";
        }

        //sources offsets - CURRENT position for drawing
        public float OffsetY;
        public float OffsetX;

        // ARCHITECTURAL FIX: Content offsets captured at rendering start
        // These are immutable during background rendering to prevent race conditions
        public float ContentOffsetY;
        public float ContentOffsetX;

        public SKColor BackgroundColor { get; set; } = SKColors.Transparent;
        public RenderObject RenderObject { get; set; }
        public SKRect Destination { get; set; }
        public SKRect LastDrawnAt { get; set; }
        public CachedObject CachedObject { get; set; }
        public SKSurface Surface { get; set; }
        public bool IsReady { get; set; } = false;
        public string Id { get; set; }

        public void Reset(SKSurface surface, SKRect source)
        {
            OffsetX = 0;
            OffsetY = 0;
            ContentOffsetX = 0;
            ContentOffsetY = 0;
            Surface = surface;
            Invalidate();
        }

        /// <summary>
        /// ARCHITECTURAL FIX: Capture current position as content offset for rendering
        /// This prevents race conditions where OffsetY changes during background rendering
        /// </summary>
        public void CaptureContentOffset()
        {
            ContentOffsetX = OffsetX;
            ContentOffsetY = OffsetY;
        }

        public void Invalidate()
        {
            IsReady = false;
            LastDrawnAt = SKRect.Empty;
        }
    }
}
