### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  id: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ActionOnTickAnimator
  - DrawnUi.Draw.AddGestures
  - DrawnUi.Draw.AddGestures.GestureListener
  - DrawnUi.Draw.AdjustBrightnessEffect
  - DrawnUi.Draw.AdjustRGBEffect
  - DrawnUi.Draw.AnimateExtensions
  - DrawnUi.Draw.AnimatorBase
  - DrawnUi.Draw.ApplySpan
  - DrawnUi.Draw.AutoSizeType
  - DrawnUi.Draw.BaseChainedEffect
  - DrawnUi.Draw.BaseColorFilterEffect
  - DrawnUi.Draw.BaseImageFilterEffect
  - DrawnUi.Draw.BevelType
  - DrawnUi.Draw.BindToParentContextExtension
  - DrawnUi.Draw.BindablePropertyExtension
  - DrawnUi.Draw.BlinkAnimator
  - DrawnUi.Draw.BlurEffect
  - DrawnUi.Draw.CachedGradient
  - DrawnUi.Draw.CachedObject
  - DrawnUi.Draw.CachedShader
  - DrawnUi.Draw.CachedShadow
  - DrawnUi.Draw.CellWIthHeight
  - DrawnUi.Draw.ChainAdjustBrightnessEffect
  - DrawnUi.Draw.ChainAdjustContrastEffect
  - DrawnUi.Draw.ChainAdjustLightnessEffect
  - DrawnUi.Draw.ChainAdjustRGBEffect
  - DrawnUi.Draw.ChainColorPresetEffect
  - DrawnUi.Draw.ChainDropShadowsEffect
  - DrawnUi.Draw.ChainEffectResult
  - DrawnUi.Draw.ChainSaturationEffect
  - DrawnUi.Draw.ChainTintWithAlphaEffect
  - DrawnUi.Draw.ColorBlendAnimator
  - DrawnUi.Draw.ColorExtensions
  - DrawnUi.Draw.ColorPresetEffect
  - DrawnUi.Draw.ContainsPointResult
  - DrawnUi.Draw.ContentLayout
  - DrawnUi.Draw.ContextArguments
  - DrawnUi.Draw.ContrastEffect
  - DrawnUi.Draw.ControlInStack
  - DrawnUi.Draw.ControlsTracker
  - DrawnUi.Draw.CriticallyDampedSpringTimingParameters
  - DrawnUi.Draw.CriticallyDampedSpringTimingVectorParameters
  - DrawnUi.Draw.DataContextIterator
  - DrawnUi.Draw.DebugImage
  - DrawnUi.Draw.DecelerationTimingParameters
  - DrawnUi.Draw.DecelerationTimingVectorParameters
  - DrawnUi.Draw.DescendingZIndexGestureListenerComparer
  - DrawnUi.Draw.DirectionType
  - DrawnUi.Draw.DrawImageAlignment
  - DrawnUi.Draw.DrawTextAlignment
  - DrawnUi.Draw.DrawingContext
  - DrawnUi.Draw.DrawingRect
  - DrawnUi.Draw.DrawnExtensions
  - DrawnUi.Draw.DrawnFontAttributesConverter
  - DrawnUi.Draw.DrawnUiStartupSettings
  - DrawnUi.Draw.DropShadowEffect
  - DrawnUi.Draw.DynamicGrid`1
  - DrawnUi.Draw.EdgeGlowAnimator
  - DrawnUi.Draw.ElementRenderer
  - DrawnUi.Draw.FindTagExtension
  - DrawnUi.Draw.FluentExtensions
  - DrawnUi.Draw.FontWeight
  - DrawnUi.Draw.FrameTimeInterpolator
  - DrawnUi.Draw.GestureEventProcessingInfo
  - DrawnUi.Draw.GesturesMode
  - DrawnUi.Draw.GlowPosition
  - DrawnUi.Draw.GradientType
  - DrawnUi.Draw.IAfterEffectDelete
  - DrawnUi.Draw.IAnimatorsManager
  - DrawnUi.Draw.IBindingContextDebuggable
  - DrawnUi.Draw.ICanBeUpdated
  - DrawnUi.Draw.ICanBeUpdatedWithContext
  - DrawnUi.Draw.ICanRenderOnCanvas
  - DrawnUi.Draw.IColorEffect
  - DrawnUi.Draw.IDampingTimingParameters
  - DrawnUi.Draw.IDampingTimingVectorParameters
  - DrawnUi.Draw.IDefinesViewport
  - DrawnUi.Draw.IDrawnBase
  - DrawnUi.Draw.IDrawnTextSpan
  - DrawnUi.Draw.IHasAfterEffects
  - DrawnUi.Draw.IHasBanner
  - DrawnUi.Draw.IImageEffect
  - DrawnUi.Draw.IInsideViewport
  - DrawnUi.Draw.IInsideWheelStack
  - DrawnUi.Draw.IInterpolator
  - DrawnUi.Draw.ILayoutInsideViewport
  - DrawnUi.Draw.IOverlayEffect
  - DrawnUi.Draw.IPostRendererEffect
  - DrawnUi.Draw.IRefreshIndicator
  - DrawnUi.Draw.IRenderEffect
  - DrawnUi.Draw.IRenderObject
  - DrawnUi.Draw.ISkiaAnimator
  - DrawnUi.Draw.ISkiaCell
  - DrawnUi.Draw.ISkiaControl
  - DrawnUi.Draw.ISkiaDrawable
  - DrawnUi.Draw.ISkiaEffect
  - DrawnUi.Draw.ISkiaGestureListener
  - DrawnUi.Draw.ISkiaGestureProcessor
  - DrawnUi.Draw.ISkiaGridLayout
  - DrawnUi.Draw.ISkiaLayer
  - DrawnUi.Draw.ISkiaLayout
  - DrawnUi.Draw.ISkiaSharpView
  - DrawnUi.Draw.IStateEffect
  - DrawnUi.Draw.ITimingParameters
  - DrawnUi.Draw.ITimingVectorParameters
  - DrawnUi.Draw.IVisibilityAware
  - DrawnUi.Draw.IWithContent
  - DrawnUi.Draw.InfiniteLayout
  - DrawnUi.Draw.KeyboardManager
  - DrawnUi.Draw.LayoutStructure
  - DrawnUi.Draw.LayoutType
  - DrawnUi.Draw.LineGlyph
  - DrawnUi.Draw.LineSpan
  - DrawnUi.Draw.LinearDirectionType
  - DrawnUi.Draw.LinearInterpolationTimingParameters
  - DrawnUi.Draw.LoadPriority
  - DrawnUi.Draw.LoadedImageSource
  - DrawnUi.Draw.LockTouch
  - DrawnUi.Draw.Looper
  - DrawnUi.Draw.LottieRefreshIndicator
  - DrawnUi.Draw.MauiKey
  - DrawnUi.Draw.MauiKeyMapper
  - DrawnUi.Draw.MeasureRequest
  - DrawnUi.Draw.MeasuredListCell
  - DrawnUi.Draw.MeasuredListCells
  - DrawnUi.Draw.MeasuringStrategy
  - DrawnUi.Draw.ObjectAliveType
  - DrawnUi.Draw.ObservableAttachedItemsCollection`1
  - DrawnUi.Draw.OrientationType
  - DrawnUi.Draw.PanningModeType
  - DrawnUi.Draw.PendulumAnimator
  - DrawnUi.Draw.PerpetualPendulumAnimator
  - DrawnUi.Draw.PingPongAnimator
  - DrawnUi.Draw.Plane
  - DrawnUi.Draw.PlanesScroll
  - DrawnUi.Draw.PlanesScroll.ViewLayoutInfo
  - DrawnUi.Draw.PointIsInsideResult
  - DrawnUi.Draw.PointedDirectionType
  - DrawnUi.Draw.PrebuiltControlStyle
  - DrawnUi.Draw.ProgressAnimator
  - DrawnUi.Draw.ProgressTrail
  - DrawnUi.Draw.RangeAnimator
  - DrawnUi.Draw.RangeF
  - DrawnUi.Draw.RangeVectorAnimator
  - DrawnUi.Draw.RangeZone
  - DrawnUi.Draw.RecycleTemplateType
  - DrawnUi.Draw.RecyclingTemplate
  - DrawnUi.Draw.RefreshIndicator
  - DrawnUi.Draw.RelativePositionType
  - DrawnUi.Draw.RenderDrawingContext
  - DrawnUi.Draw.RenderLabel
  - DrawnUi.Draw.RenderObject
  - DrawnUi.Draw.RenderTreeRenderer
  - DrawnUi.Draw.RenderingAnimator
  - DrawnUi.Draw.RenderingModeType
  - DrawnUi.Draw.RippleAnimator
  - DrawnUi.Draw.SaturationEffect
  - DrawnUi.Draw.ScaledPoint
  - DrawnUi.Draw.ScaledRect
  - DrawnUi.Draw.ScaledSize
  - DrawnUi.Draw.ScrollFlingAnimator
  - DrawnUi.Draw.ScrollFlingVectorAnimator
  - DrawnUi.Draw.ScrollInteractionState
  - DrawnUi.Draw.ScrollToIndexOrder
  - DrawnUi.Draw.ScrollToPointOrder
  - DrawnUi.Draw.ShaderDoubleTexturesEffect
  - DrawnUi.Draw.ShapeType
  - DrawnUi.Draw.ShimmerAnimator
  - DrawnUi.Draw.SidePosition
  - DrawnUi.Draw.Sk3dView
  - DrawnUi.Draw.SkCamera3D
  - DrawnUi.Draw.SkCamera3D2
  - DrawnUi.Draw.SkPatch3D
  - DrawnUi.Draw.SkiaAnchorBak
  - DrawnUi.Draw.SkiaBackdrop
  - DrawnUi.Draw.SkiaBevel
  - DrawnUi.Draw.SkiaButton
  - DrawnUi.Draw.SkiaButton.ButtonLabel
  - DrawnUi.Draw.SkiaButton.ButtonStyleType
  - DrawnUi.Draw.SkiaButton.IconPositionType
  - DrawnUi.Draw.SkiaCacheType
  - DrawnUi.Draw.SkiaCheckbox
  - DrawnUi.Draw.SkiaControl
  - DrawnUi.Draw.SkiaControl.CacheValidityType
  - DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
  - DrawnUi.Draw.SkiaControl.ParentMeasureRequest
  - DrawnUi.Draw.SkiaControlWithRect
  - DrawnUi.Draw.SkiaControlsObservable
  - DrawnUi.Draw.SkiaCursor
  - DrawnUi.Draw.SkiaDoubleAttachedTexturesEffect
  - DrawnUi.Draw.SkiaDrawingContext
  - DrawnUi.Draw.SkiaEditor
  - DrawnUi.Draw.SkiaEffect
  - DrawnUi.Draw.SkiaFontManager
  - DrawnUi.Draw.SkiaFrame
  - DrawnUi.Draw.SkiaGesturesInfo
  - DrawnUi.Draw.SkiaGesturesParameters
  - DrawnUi.Draw.SkiaGradient
  - DrawnUi.Draw.SkiaGrid
  - DrawnUi.Draw.SkiaHotspot
  - DrawnUi.Draw.SkiaHotspotZoom
  - DrawnUi.Draw.SkiaHoverMask
  - DrawnUi.Draw.SkiaImage
  - DrawnUi.Draw.SkiaImage.RescaledBitmap
  - DrawnUi.Draw.SkiaImageEffect
  - DrawnUi.Draw.SkiaImageEffects
  - DrawnUi.Draw.SkiaImageManager
  - DrawnUi.Draw.SkiaImageManager.QueueItem
  - DrawnUi.Draw.SkiaImageTiles
  - DrawnUi.Draw.SkiaLabel
  - DrawnUi.Draw.SkiaLabel.DecomposedText
  - DrawnUi.Draw.SkiaLabel.EmojiData
  - DrawnUi.Draw.SkiaLabel.ObjectPools
  - DrawnUi.Draw.SkiaLabel.PooledStringBuilder
  - DrawnUi.Draw.SkiaLabel.SpanCollection
  - DrawnUi.Draw.SkiaLabel.SpanMeasurement
  - DrawnUi.Draw.SkiaLabel.TextMetrics
  - DrawnUi.Draw.SkiaLabelFps
  - DrawnUi.Draw.SkiaLayer
  - DrawnUi.Draw.SkiaLayout
  - DrawnUi.Draw.SkiaLayout.BuildWrapLayout
  - DrawnUi.Draw.SkiaLayout.Cell
  - DrawnUi.Draw.SkiaLayout.SecondPassArrange
  - DrawnUi.Draw.SkiaLayout.SkiaGridStructure
  - DrawnUi.Draw.SkiaMauiElement
  - DrawnUi.Draw.SkiaPoint
  - DrawnUi.Draw.SkiaProgress
  - DrawnUi.Draw.SkiaRangeBase
  - DrawnUi.Draw.SkiaRichLabel
  - DrawnUi.Draw.SkiaRow
  - DrawnUi.Draw.SkiaScroll
  - DrawnUi.Draw.SkiaScrollLooped
  - DrawnUi.Draw.SkiaSetter
  - DrawnUi.Draw.SkiaShaderEffect
  - DrawnUi.Draw.SkiaShadow
  - DrawnUi.Draw.SkiaShape
  - DrawnUi.Draw.SkiaShape.ShapePaintArguments
  - DrawnUi.Draw.SkiaSlider
  - DrawnUi.Draw.SkiaStack
  - DrawnUi.Draw.SkiaSvg
  - DrawnUi.Draw.SkiaSwitch
  - DrawnUi.Draw.SkiaToggle
  - DrawnUi.Draw.SkiaTouchAnimation
  - DrawnUi.Draw.SkiaValueAnimator
  - DrawnUi.Draw.SkiaVectorAnimator
  - DrawnUi.Draw.SkiaWrap
  - DrawnUi.Draw.SliderThumb
  - DrawnUi.Draw.SliderTrail
  - DrawnUi.Draw.SliderValueDesc
  - DrawnUi.Draw.SnapToChildrenType
  - DrawnUi.Draw.Snapping
  - DrawnUi.Draw.SnappingLayout
  - DrawnUi.Draw.SortedGestureListeners
  - DrawnUi.Draw.SourceType
  - DrawnUi.Draw.SpaceDistribution
  - DrawnUi.Draw.SpringExtensions
  - DrawnUi.Draw.SpringTimingParameters
  - DrawnUi.Draw.SpringTimingVectorParameters
  - DrawnUi.Draw.SpringWithVelocityAnimator
  - DrawnUi.Draw.SpringWithVelocityVectorAnimator
  - DrawnUi.Draw.StackLayoutStructure
  - DrawnUi.Draw.StateEffect
  - DrawnUi.Draw.StaticResourcesExtensions
  - DrawnUi.Draw.StringReference
  - DrawnUi.Draw.Super
  - DrawnUi.Draw.SvgSpan
  - DrawnUi.Draw.TemplatedViewsPool
  - DrawnUi.Draw.TextLine
  - DrawnUi.Draw.TextSpan
  - DrawnUi.Draw.TextTransform
  - DrawnUi.Draw.TintEffect
  - DrawnUi.Draw.TintWithAlphaEffect
  - DrawnUi.Draw.ToggleAnimator
  - DrawnUi.Draw.TrackedObject`1
  - DrawnUi.Draw.TransformAspect
  - DrawnUi.Draw.UnderdampedSpringTimingParameters
  - DrawnUi.Draw.UnderdampedSpringTimingVectorParameters
  - DrawnUi.Draw.UsedGlyph
  - DrawnUi.Draw.VelocityAccumulator
  - DrawnUi.Draw.ViewportScrollType
  - DrawnUi.Draw.ViewsAdapter
  - DrawnUi.Draw.ViewsIterator
  - DrawnUi.Draw.VirtualScroll
  - DrawnUi.Draw.VirtualisationType
  - DrawnUi.Draw.ViscousFluidInterpolator
  - DrawnUi.Draw.VisualLayer
  - DrawnUi.Draw.VisualTreeHandler
  - DrawnUi.Draw.WindowParameters
  - DrawnUi.Draw.ZoomContent
  - DrawnUi.Draw.ZoomEventArgs
  langs:
  - csharp
  - vb
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  type: Namespace
  assemblies:
  - DrawnUi.Maui
references:
- uid: DrawnUi.Draw.SkiaButton
  commentId: T:DrawnUi.Draw.SkiaButton
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaButton.html
  name: SkiaButton
  nameWithType: SkiaButton
  fullName: DrawnUi.Draw.SkiaButton
- uid: DrawnUi.Draw.SkiaButton.ButtonLabel
  commentId: T:DrawnUi.Draw.SkiaButton.ButtonLabel
  href: DrawnUi.Draw.SkiaButton.html
  name: SkiaButton.ButtonLabel
  nameWithType: SkiaButton.ButtonLabel
  fullName: DrawnUi.Draw.SkiaButton.ButtonLabel
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaButton
    name: SkiaButton
    href: DrawnUi.Draw.SkiaButton.html
  - name: .
  - uid: DrawnUi.Draw.SkiaButton.ButtonLabel
    name: ButtonLabel
    href: DrawnUi.Draw.SkiaButton.ButtonLabel.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaButton
    name: SkiaButton
    href: DrawnUi.Draw.SkiaButton.html
  - name: .
  - uid: DrawnUi.Draw.SkiaButton.ButtonLabel
    name: ButtonLabel
    href: DrawnUi.Draw.SkiaButton.ButtonLabel.html
- uid: DrawnUi.Draw.SkiaButton.ButtonStyleType
  commentId: T:DrawnUi.Draw.SkiaButton.ButtonStyleType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaButton.html
  name: SkiaButton.ButtonStyleType
  nameWithType: SkiaButton.ButtonStyleType
  fullName: DrawnUi.Draw.SkiaButton.ButtonStyleType
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaButton
    name: SkiaButton
    href: DrawnUi.Draw.SkiaButton.html
  - name: .
  - uid: DrawnUi.Draw.SkiaButton.ButtonStyleType
    name: ButtonStyleType
    href: DrawnUi.Draw.SkiaButton.ButtonStyleType.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaButton
    name: SkiaButton
    href: DrawnUi.Draw.SkiaButton.html
  - name: .
  - uid: DrawnUi.Draw.SkiaButton.ButtonStyleType
    name: ButtonStyleType
    href: DrawnUi.Draw.SkiaButton.ButtonStyleType.html
- uid: DrawnUi.Draw.SkiaButton.IconPositionType
  commentId: T:DrawnUi.Draw.SkiaButton.IconPositionType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaButton.html
  name: SkiaButton.IconPositionType
  nameWithType: SkiaButton.IconPositionType
  fullName: DrawnUi.Draw.SkiaButton.IconPositionType
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaButton
    name: SkiaButton
    href: DrawnUi.Draw.SkiaButton.html
  - name: .
  - uid: DrawnUi.Draw.SkiaButton.IconPositionType
    name: IconPositionType
    href: DrawnUi.Draw.SkiaButton.IconPositionType.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaButton
    name: SkiaButton
    href: DrawnUi.Draw.SkiaButton.html
  - name: .
  - uid: DrawnUi.Draw.SkiaButton.IconPositionType
    name: IconPositionType
    href: DrawnUi.Draw.SkiaButton.IconPositionType.html
- uid: DrawnUi.Draw.SkiaLabelFps
  commentId: T:DrawnUi.Draw.SkiaLabelFps
  href: DrawnUi.Draw.SkiaLabelFps.html
  name: SkiaLabelFps
  nameWithType: SkiaLabelFps
  fullName: DrawnUi.Draw.SkiaLabelFps
- uid: DrawnUi.Draw.SkiaRichLabel
  commentId: T:DrawnUi.Draw.SkiaRichLabel
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaRichLabel.html
  name: SkiaRichLabel
  nameWithType: SkiaRichLabel
  fullName: DrawnUi.Draw.SkiaRichLabel
- uid: DrawnUi.Draw.ProgressTrail
  commentId: T:DrawnUi.Draw.ProgressTrail
  href: DrawnUi.Draw.ProgressTrail.html
  name: ProgressTrail
  nameWithType: ProgressTrail
  fullName: DrawnUi.Draw.ProgressTrail
- uid: DrawnUi.Draw.SkiaProgress
  commentId: T:DrawnUi.Draw.SkiaProgress
  href: DrawnUi.Draw.SkiaProgress.html
  name: SkiaProgress
  nameWithType: SkiaProgress
  fullName: DrawnUi.Draw.SkiaProgress
- uid: DrawnUi.Draw.SkiaRangeBase
  commentId: T:DrawnUi.Draw.SkiaRangeBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaRangeBase.html
  name: SkiaRangeBase
  nameWithType: SkiaRangeBase
  fullName: DrawnUi.Draw.SkiaRangeBase
- uid: DrawnUi.Draw.SkiaHoverMask
  commentId: T:DrawnUi.Draw.SkiaHoverMask
  href: DrawnUi.Draw.SkiaHoverMask.html
  name: SkiaHoverMask
  nameWithType: SkiaHoverMask
  fullName: DrawnUi.Draw.SkiaHoverMask
- uid: DrawnUi.Draw.SkiaSlider
  commentId: T:DrawnUi.Draw.SkiaSlider
  href: DrawnUi.Draw.SkiaSlider.html
  name: SkiaSlider
  nameWithType: SkiaSlider
  fullName: DrawnUi.Draw.SkiaSlider
- uid: DrawnUi.Draw.SliderThumb
  commentId: T:DrawnUi.Draw.SliderThumb
  href: DrawnUi.Draw.SliderThumb.html
  name: SliderThumb
  nameWithType: SliderThumb
  fullName: DrawnUi.Draw.SliderThumb
- uid: DrawnUi.Draw.SliderTrail
  commentId: T:DrawnUi.Draw.SliderTrail
  href: DrawnUi.Draw.SliderTrail.html
  name: SliderTrail
  nameWithType: SliderTrail
  fullName: DrawnUi.Draw.SliderTrail
- uid: DrawnUi.Draw.SliderValueDesc
  commentId: T:DrawnUi.Draw.SliderValueDesc
  href: DrawnUi.Draw.SliderValueDesc.html
  name: SliderValueDesc
  nameWithType: SliderValueDesc
  fullName: DrawnUi.Draw.SliderValueDesc
- uid: DrawnUi.Draw.PrebuiltControlStyle
  commentId: T:DrawnUi.Draw.PrebuiltControlStyle
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.PrebuiltControlStyle.html
  name: PrebuiltControlStyle
  nameWithType: PrebuiltControlStyle
  fullName: DrawnUi.Draw.PrebuiltControlStyle
- uid: DrawnUi.Draw.SkiaCheckbox
  commentId: T:DrawnUi.Draw.SkiaCheckbox
  href: DrawnUi.Draw.SkiaCheckbox.html
  name: SkiaCheckbox
  nameWithType: SkiaCheckbox
  fullName: DrawnUi.Draw.SkiaCheckbox
- uid: DrawnUi.Draw.SkiaSwitch
  commentId: T:DrawnUi.Draw.SkiaSwitch
  href: DrawnUi.Draw.SkiaSwitch.html
  name: SkiaSwitch
  nameWithType: SkiaSwitch
  fullName: DrawnUi.Draw.SkiaSwitch
- uid: DrawnUi.Draw.SkiaToggle
  commentId: T:DrawnUi.Draw.SkiaToggle
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaToggle.html
  name: SkiaToggle
  nameWithType: SkiaToggle
  fullName: DrawnUi.Draw.SkiaToggle
- uid: DrawnUi.Draw.ZoomContent
  commentId: T:DrawnUi.Draw.ZoomContent
  href: DrawnUi.Draw.ZoomContent.html
  name: ZoomContent
  nameWithType: ZoomContent
  fullName: DrawnUi.Draw.ZoomContent
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Draw.SkiaControl.CacheValidityType
  commentId: T:DrawnUi.Draw.SkiaControl.CacheValidityType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl.CacheValidityType
  nameWithType: SkiaControl.CacheValidityType
  fullName: DrawnUi.Draw.SkiaControl.CacheValidityType
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: .
  - uid: DrawnUi.Draw.SkiaControl.CacheValidityType
    name: CacheValidityType
    href: DrawnUi.Draw.SkiaControl.CacheValidityType.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: .
  - uid: DrawnUi.Draw.SkiaControl.CacheValidityType
    name: CacheValidityType
    href: DrawnUi.Draw.SkiaControl.CacheValidityType.html
- uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
  commentId: T:DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl.ControlTappedEventArgs
  nameWithType: SkiaControl.ControlTappedEventArgs
  fullName: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: .
  - uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
    name: ControlTappedEventArgs
    href: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: .
  - uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
    name: ControlTappedEventArgs
    href: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.html
- uid: DrawnUi.Draw.SkiaControl.ParentMeasureRequest
  commentId: T:DrawnUi.Draw.SkiaControl.ParentMeasureRequest
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl.ParentMeasureRequest
  nameWithType: SkiaControl.ParentMeasureRequest
  fullName: DrawnUi.Draw.SkiaControl.ParentMeasureRequest
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: .
  - uid: DrawnUi.Draw.SkiaControl.ParentMeasureRequest
    name: ParentMeasureRequest
    href: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: .
  - uid: DrawnUi.Draw.SkiaControl.ParentMeasureRequest
    name: ParentMeasureRequest
    href: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.html
- uid: DrawnUi.Draw.SkiaCursor
  commentId: T:DrawnUi.Draw.SkiaCursor
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaCursor.html
  name: SkiaCursor
  nameWithType: SkiaCursor
  fullName: DrawnUi.Draw.SkiaCursor
- uid: DrawnUi.Draw.SkiaEditor
  commentId: T:DrawnUi.Draw.SkiaEditor
  href: DrawnUi.Draw.SkiaEditor.html
  name: SkiaEditor
  nameWithType: SkiaEditor
  fullName: DrawnUi.Draw.SkiaEditor
- uid: DrawnUi.Draw.SkiaMauiElement
  commentId: T:DrawnUi.Draw.SkiaMauiElement
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaMauiElement.html
  name: SkiaMauiElement
  nameWithType: SkiaMauiElement
  fullName: DrawnUi.Draw.SkiaMauiElement
- uid: DrawnUi.Draw.LoadedImageSource
  commentId: T:DrawnUi.Draw.LoadedImageSource
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LoadedImageSource.html
  name: LoadedImageSource
  nameWithType: LoadedImageSource
  fullName: DrawnUi.Draw.LoadedImageSource
- uid: DrawnUi.Draw.SkiaImage
  commentId: T:DrawnUi.Draw.SkiaImage
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaImage.html
  name: SkiaImage
  nameWithType: SkiaImage
  fullName: DrawnUi.Draw.SkiaImage
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap
  commentId: T:DrawnUi.Draw.SkiaImage.RescaledBitmap
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaImage.html
  name: SkiaImage.RescaledBitmap
  nameWithType: SkiaImage.RescaledBitmap
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaImage
    name: SkiaImage
    href: DrawnUi.Draw.SkiaImage.html
  - name: .
  - uid: DrawnUi.Draw.SkiaImage.RescaledBitmap
    name: RescaledBitmap
    href: DrawnUi.Draw.SkiaImage.RescaledBitmap.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaImage
    name: SkiaImage
    href: DrawnUi.Draw.SkiaImage.html
  - name: .
  - uid: DrawnUi.Draw.SkiaImage.RescaledBitmap
    name: RescaledBitmap
    href: DrawnUi.Draw.SkiaImage.RescaledBitmap.html
- uid: DrawnUi.Draw.SkiaImageTiles
  commentId: T:DrawnUi.Draw.SkiaImageTiles
  href: DrawnUi.Draw.SkiaImageTiles.html
  name: SkiaImageTiles
  nameWithType: SkiaImageTiles
  fullName: DrawnUi.Draw.SkiaImageTiles
- uid: DrawnUi.Draw.ContentLayout
  commentId: T:DrawnUi.Draw.ContentLayout
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ContentLayout.html
  name: ContentLayout
  nameWithType: ContentLayout
  fullName: DrawnUi.Draw.ContentLayout
- uid: DrawnUi.Draw.InfiniteLayout
  commentId: T:DrawnUi.Draw.InfiniteLayout
  href: DrawnUi.Draw.InfiniteLayout.html
  name: InfiniteLayout
  nameWithType: InfiniteLayout
  fullName: DrawnUi.Draw.InfiniteLayout
- uid: DrawnUi.Draw.LayoutStructure
  commentId: T:DrawnUi.Draw.LayoutStructure
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LayoutStructure.html
  name: LayoutStructure
  nameWithType: LayoutStructure
  fullName: DrawnUi.Draw.LayoutStructure
- uid: DrawnUi.Draw.SkiaControlWithRect
  commentId: T:DrawnUi.Draw.SkiaControlWithRect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControlWithRect.html
  name: SkiaControlWithRect
  nameWithType: SkiaControlWithRect
  fullName: DrawnUi.Draw.SkiaControlWithRect
- uid: DrawnUi.Draw.IRenderObject
  commentId: T:DrawnUi.Draw.IRenderObject
  href: DrawnUi.Draw.IRenderObject.html
  name: IRenderObject
  nameWithType: IRenderObject
  fullName: DrawnUi.Draw.IRenderObject
- uid: DrawnUi.Draw.ElementRenderer
  commentId: T:DrawnUi.Draw.ElementRenderer
  href: DrawnUi.Draw.ElementRenderer.html
  name: ElementRenderer
  nameWithType: ElementRenderer
  fullName: DrawnUi.Draw.ElementRenderer
- uid: DrawnUi.Draw.RenderTreeRenderer
  commentId: T:DrawnUi.Draw.RenderTreeRenderer
  href: DrawnUi.Draw.RenderTreeRenderer.html
  name: RenderTreeRenderer
  nameWithType: RenderTreeRenderer
  fullName: DrawnUi.Draw.RenderTreeRenderer
- uid: DrawnUi.Draw.RenderObject
  commentId: T:DrawnUi.Draw.RenderObject
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RenderObject.html
  name: RenderObject
  nameWithType: RenderObject
  fullName: DrawnUi.Draw.RenderObject
- uid: DrawnUi.Draw.RenderLabel
  commentId: T:DrawnUi.Draw.RenderLabel
  href: DrawnUi.Draw.RenderLabel.html
  name: RenderLabel
  nameWithType: RenderLabel
  fullName: DrawnUi.Draw.RenderLabel
- uid: DrawnUi.Draw.SkiaLayout
  commentId: T:DrawnUi.Draw.SkiaLayout
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaLayout.html
  name: SkiaLayout
  nameWithType: SkiaLayout
  fullName: DrawnUi.Draw.SkiaLayout
- uid: DrawnUi.Draw.SkiaLayout.BuildWrapLayout
  commentId: T:DrawnUi.Draw.SkiaLayout.BuildWrapLayout
  href: DrawnUi.Draw.SkiaLayout.html
  name: SkiaLayout.BuildWrapLayout
  nameWithType: SkiaLayout.BuildWrapLayout
  fullName: DrawnUi.Draw.SkiaLayout.BuildWrapLayout
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.BuildWrapLayout
    name: BuildWrapLayout
    href: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.BuildWrapLayout
    name: BuildWrapLayout
    href: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.html
- uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange
  commentId: T:DrawnUi.Draw.SkiaLayout.SecondPassArrange
  href: DrawnUi.Draw.SkiaLayout.html
  name: SkiaLayout.SecondPassArrange
  nameWithType: SkiaLayout.SecondPassArrange
  fullName: DrawnUi.Draw.SkiaLayout.SecondPassArrange
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange
    name: SecondPassArrange
    href: DrawnUi.Draw.SkiaLayout.SecondPassArrange.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange
    name: SecondPassArrange
    href: DrawnUi.Draw.SkiaLayout.SecondPassArrange.html
- uid: DrawnUi.Draw.SkiaLayout.Cell
  commentId: T:DrawnUi.Draw.SkiaLayout.Cell
  href: DrawnUi.Draw.SkiaLayout.html
  name: SkiaLayout.Cell
  nameWithType: SkiaLayout.Cell
  fullName: DrawnUi.Draw.SkiaLayout.Cell
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.Cell
    name: Cell
    href: DrawnUi.Draw.SkiaLayout.Cell.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.Cell
    name: Cell
    href: DrawnUi.Draw.SkiaLayout.Cell.html
- uid: DrawnUi.Draw.SkiaLayout.SkiaGridStructure
  commentId: T:DrawnUi.Draw.SkiaLayout.SkiaGridStructure
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaLayout.html
  name: SkiaLayout.SkiaGridStructure
  nameWithType: SkiaLayout.SkiaGridStructure
  fullName: DrawnUi.Draw.SkiaLayout.SkiaGridStructure
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.SkiaGridStructure
    name: SkiaGridStructure
    href: DrawnUi.Draw.SkiaLayout.SkiaGridStructure.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.SkiaGridStructure
    name: SkiaGridStructure
    href: DrawnUi.Draw.SkiaLayout.SkiaGridStructure.html
- uid: DrawnUi.Draw.MeasuredListCell
  commentId: T:DrawnUi.Draw.MeasuredListCell
  href: DrawnUi.Draw.MeasuredListCell.html
  name: MeasuredListCell
  nameWithType: MeasuredListCell
  fullName: DrawnUi.Draw.MeasuredListCell
- uid: DrawnUi.Draw.MeasuredListCells
  commentId: T:DrawnUi.Draw.MeasuredListCells
  href: DrawnUi.Draw.MeasuredListCells.html
  name: MeasuredListCells
  nameWithType: MeasuredListCells
  fullName: DrawnUi.Draw.MeasuredListCells
- uid: DrawnUi.Draw.CellWIthHeight
  commentId: T:DrawnUi.Draw.CellWIthHeight
  href: DrawnUi.Draw.CellWIthHeight.html
  name: CellWIthHeight
  nameWithType: CellWIthHeight
  fullName: DrawnUi.Draw.CellWIthHeight
- uid: DrawnUi.Draw.ViewsAdapter
  commentId: T:DrawnUi.Draw.ViewsAdapter
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ViewsAdapter.html
  name: ViewsAdapter
  nameWithType: ViewsAdapter
  fullName: DrawnUi.Draw.ViewsAdapter
- uid: DrawnUi.Draw.ViewsIterator
  commentId: T:DrawnUi.Draw.ViewsIterator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ViewsIterator.html
  name: ViewsIterator
  nameWithType: ViewsIterator
  fullName: DrawnUi.Draw.ViewsIterator
- uid: DrawnUi.Draw.DataContextIterator
  commentId: T:DrawnUi.Draw.DataContextIterator
  href: DrawnUi.Draw.DataContextIterator.html
  name: DataContextIterator
  nameWithType: DataContextIterator
  fullName: DrawnUi.Draw.DataContextIterator
- uid: DrawnUi.Draw.TemplatedViewsPool
  commentId: T:DrawnUi.Draw.TemplatedViewsPool
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.TemplatedViewsPool.html
  name: TemplatedViewsPool
  nameWithType: TemplatedViewsPool
  fullName: DrawnUi.Draw.TemplatedViewsPool
- uid: DrawnUi.Draw.SnappingLayout
  commentId: T:DrawnUi.Draw.SnappingLayout
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SnappingLayout.html
  name: SnappingLayout
  nameWithType: SnappingLayout
  fullName: DrawnUi.Draw.SnappingLayout
- uid: DrawnUi.Draw.StackLayoutStructure
  commentId: T:DrawnUi.Draw.StackLayoutStructure
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.StackLayoutStructure.html
  name: StackLayoutStructure
  nameWithType: StackLayoutStructure
  fullName: DrawnUi.Draw.StackLayoutStructure
- uid: DrawnUi.Draw.LottieRefreshIndicator
  commentId: T:DrawnUi.Draw.LottieRefreshIndicator
  href: DrawnUi.Draw.LottieRefreshIndicator.html
  name: LottieRefreshIndicator
  nameWithType: LottieRefreshIndicator
  fullName: DrawnUi.Draw.LottieRefreshIndicator
- uid: DrawnUi.Draw.RefreshIndicator
  commentId: T:DrawnUi.Draw.RefreshIndicator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RefreshIndicator.html
  name: RefreshIndicator
  nameWithType: RefreshIndicator
  fullName: DrawnUi.Draw.RefreshIndicator
- uid: DrawnUi.Draw.Plane
  commentId: T:DrawnUi.Draw.Plane
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.Plane.html
  name: Plane
  nameWithType: Plane
  fullName: DrawnUi.Draw.Plane
- uid: DrawnUi.Draw.PlanesScroll
  commentId: T:DrawnUi.Draw.PlanesScroll
  href: DrawnUi.Draw.PlanesScroll.html
  name: PlanesScroll
  nameWithType: PlanesScroll
  fullName: DrawnUi.Draw.PlanesScroll
- uid: DrawnUi.Draw.PlanesScroll.ViewLayoutInfo
  commentId: T:DrawnUi.Draw.PlanesScroll.ViewLayoutInfo
  href: DrawnUi.Draw.PlanesScroll.html
  name: PlanesScroll.ViewLayoutInfo
  nameWithType: PlanesScroll.ViewLayoutInfo
  fullName: DrawnUi.Draw.PlanesScroll.ViewLayoutInfo
  spec.csharp:
  - uid: DrawnUi.Draw.PlanesScroll
    name: PlanesScroll
    href: DrawnUi.Draw.PlanesScroll.html
  - name: .
  - uid: DrawnUi.Draw.PlanesScroll.ViewLayoutInfo
    name: ViewLayoutInfo
    href: DrawnUi.Draw.PlanesScroll.ViewLayoutInfo.html
  spec.vb:
  - uid: DrawnUi.Draw.PlanesScroll
    name: PlanesScroll
    href: DrawnUi.Draw.PlanesScroll.html
  - name: .
  - uid: DrawnUi.Draw.PlanesScroll.ViewLayoutInfo
    name: ViewLayoutInfo
    href: DrawnUi.Draw.PlanesScroll.ViewLayoutInfo.html
- uid: DrawnUi.Draw.ScrollToIndexOrder
  commentId: T:DrawnUi.Draw.ScrollToIndexOrder
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScrollToIndexOrder.html
  name: ScrollToIndexOrder
  nameWithType: ScrollToIndexOrder
  fullName: DrawnUi.Draw.ScrollToIndexOrder
- uid: DrawnUi.Draw.VelocityAccumulator
  commentId: T:DrawnUi.Draw.VelocityAccumulator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.VelocityAccumulator.html
  name: VelocityAccumulator
  nameWithType: VelocityAccumulator
  fullName: DrawnUi.Draw.VelocityAccumulator
- uid: DrawnUi.Draw.ScrollToPointOrder
  commentId: T:DrawnUi.Draw.ScrollToPointOrder
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScrollToPointOrder.html
  name: ScrollToPointOrder
  nameWithType: ScrollToPointOrder
  fullName: DrawnUi.Draw.ScrollToPointOrder
- uid: DrawnUi.Draw.SkiaScroll
  commentId: T:DrawnUi.Draw.SkiaScroll
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaScroll.html
  name: SkiaScroll
  nameWithType: SkiaScroll
  fullName: DrawnUi.Draw.SkiaScroll
- uid: DrawnUi.Draw.SkiaScrollLooped
  commentId: T:DrawnUi.Draw.SkiaScrollLooped
  href: DrawnUi.Draw.SkiaScrollLooped.html
  name: SkiaScrollLooped
  nameWithType: SkiaScrollLooped
  fullName: DrawnUi.Draw.SkiaScrollLooped
- uid: DrawnUi.Draw.VirtualScroll
  commentId: T:DrawnUi.Draw.VirtualScroll
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.VirtualScroll.html
  name: VirtualScroll
  nameWithType: VirtualScroll
  fullName: DrawnUi.Draw.VirtualScroll
- uid: DrawnUi.Draw.SkiaBackdrop
  commentId: T:DrawnUi.Draw.SkiaBackdrop
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaBackdrop.html
  name: SkiaBackdrop
  nameWithType: SkiaBackdrop
  fullName: DrawnUi.Draw.SkiaBackdrop
- uid: DrawnUi.Draw.SkiaControlsObservable
  commentId: T:DrawnUi.Draw.SkiaControlsObservable
  href: DrawnUi.Draw.SkiaControlsObservable.html
  name: SkiaControlsObservable
  nameWithType: SkiaControlsObservable
  fullName: DrawnUi.Draw.SkiaControlsObservable
- uid: DrawnUi.Draw.SkiaHotspot
  commentId: T:DrawnUi.Draw.SkiaHotspot
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaHotspot.html
  name: SkiaHotspot
  nameWithType: SkiaHotspot
  fullName: DrawnUi.Draw.SkiaHotspot
- uid: DrawnUi.Draw.SkiaHotspotZoom
  commentId: T:DrawnUi.Draw.SkiaHotspotZoom
  href: DrawnUi.Draw.SkiaHotspotZoom.html
  name: SkiaHotspotZoom
  nameWithType: SkiaHotspotZoom
  fullName: DrawnUi.Draw.SkiaHotspotZoom
- uid: DrawnUi.Draw.SkiaShape
  commentId: T:DrawnUi.Draw.SkiaShape
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaShape.html
  name: SkiaShape
  nameWithType: SkiaShape
  fullName: DrawnUi.Draw.SkiaShape
- uid: DrawnUi.Draw.SkiaShape.ShapePaintArguments
  commentId: T:DrawnUi.Draw.SkiaShape.ShapePaintArguments
  href: DrawnUi.Draw.SkiaShape.html
  name: SkiaShape.ShapePaintArguments
  nameWithType: SkiaShape.ShapePaintArguments
  fullName: DrawnUi.Draw.SkiaShape.ShapePaintArguments
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaShape
    name: SkiaShape
    href: DrawnUi.Draw.SkiaShape.html
  - name: .
  - uid: DrawnUi.Draw.SkiaShape.ShapePaintArguments
    name: ShapePaintArguments
    href: DrawnUi.Draw.SkiaShape.ShapePaintArguments.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaShape
    name: SkiaShape
    href: DrawnUi.Draw.SkiaShape.html
  - name: .
  - uid: DrawnUi.Draw.SkiaShape.ShapePaintArguments
    name: ShapePaintArguments
    href: DrawnUi.Draw.SkiaShape.ShapePaintArguments.html
- uid: DrawnUi.Draw.SkiaSvg
  commentId: T:DrawnUi.Draw.SkiaSvg
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaSvg.html
  name: SkiaSvg
  nameWithType: SkiaSvg
  fullName: DrawnUi.Draw.SkiaSvg
- uid: DrawnUi.Draw.ApplySpan
  commentId: T:DrawnUi.Draw.ApplySpan
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ApplySpan.html
  name: ApplySpan
  nameWithType: ApplySpan
  fullName: DrawnUi.Draw.ApplySpan
- uid: DrawnUi.Draw.IDrawnTextSpan
  commentId: T:DrawnUi.Draw.IDrawnTextSpan
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDrawnTextSpan.html
  name: IDrawnTextSpan
  nameWithType: IDrawnTextSpan
  fullName: DrawnUi.Draw.IDrawnTextSpan
- uid: DrawnUi.Draw.LineGlyph
  commentId: T:DrawnUi.Draw.LineGlyph
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LineGlyph.html
  name: LineGlyph
  nameWithType: LineGlyph
  fullName: DrawnUi.Draw.LineGlyph
- uid: DrawnUi.Draw.LineSpan
  commentId: T:DrawnUi.Draw.LineSpan
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LineSpan.html
  name: LineSpan
  nameWithType: LineSpan
  fullName: DrawnUi.Draw.LineSpan
- uid: DrawnUi.Draw.SkiaLabel
  commentId: T:DrawnUi.Draw.SkiaLabel
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaLabel.html
  name: SkiaLabel
  nameWithType: SkiaLabel
  fullName: DrawnUi.Draw.SkiaLabel
- uid: DrawnUi.Draw.SkiaLabel.SpanCollection
  commentId: T:DrawnUi.Draw.SkiaLabel.SpanCollection
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaLabel.html
  name: SkiaLabel.SpanCollection
  nameWithType: SkiaLabel.SpanCollection
  fullName: DrawnUi.Draw.SkiaLabel.SpanCollection
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.SpanCollection
    name: SpanCollection
    href: DrawnUi.Draw.SkiaLabel.SpanCollection.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.SpanCollection
    name: SpanCollection
    href: DrawnUi.Draw.SkiaLabel.SpanCollection.html
- uid: DrawnUi.Draw.SkiaLabel.EmojiData
  commentId: T:DrawnUi.Draw.SkiaLabel.EmojiData
  href: DrawnUi.Draw.SkiaLabel.html
  name: SkiaLabel.EmojiData
  nameWithType: SkiaLabel.EmojiData
  fullName: DrawnUi.Draw.SkiaLabel.EmojiData
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.EmojiData
    name: EmojiData
    href: DrawnUi.Draw.SkiaLabel.EmojiData.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.EmojiData
    name: EmojiData
    href: DrawnUi.Draw.SkiaLabel.EmojiData.html
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics
  commentId: T:DrawnUi.Draw.SkiaLabel.TextMetrics
  href: DrawnUi.Draw.SkiaLabel.html
  name: SkiaLabel.TextMetrics
  nameWithType: SkiaLabel.TextMetrics
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.TextMetrics
    name: TextMetrics
    href: DrawnUi.Draw.SkiaLabel.TextMetrics.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.TextMetrics
    name: TextMetrics
    href: DrawnUi.Draw.SkiaLabel.TextMetrics.html
- uid: DrawnUi.Draw.SkiaLabel.DecomposedText
  commentId: T:DrawnUi.Draw.SkiaLabel.DecomposedText
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaLabel.html
  name: SkiaLabel.DecomposedText
  nameWithType: SkiaLabel.DecomposedText
  fullName: DrawnUi.Draw.SkiaLabel.DecomposedText
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.DecomposedText
    name: DecomposedText
    href: DrawnUi.Draw.SkiaLabel.DecomposedText.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.DecomposedText
    name: DecomposedText
    href: DrawnUi.Draw.SkiaLabel.DecomposedText.html
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools
  commentId: T:DrawnUi.Draw.SkiaLabel.ObjectPools
  href: DrawnUi.Draw.SkiaLabel.html
  name: SkiaLabel.ObjectPools
  nameWithType: SkiaLabel.ObjectPools
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.ObjectPools
    name: ObjectPools
    href: DrawnUi.Draw.SkiaLabel.ObjectPools.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.ObjectPools
    name: ObjectPools
    href: DrawnUi.Draw.SkiaLabel.ObjectPools.html
- uid: DrawnUi.Draw.SkiaLabel.PooledStringBuilder
  commentId: T:DrawnUi.Draw.SkiaLabel.PooledStringBuilder
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaLabel.html
  name: SkiaLabel.PooledStringBuilder
  nameWithType: SkiaLabel.PooledStringBuilder
  fullName: DrawnUi.Draw.SkiaLabel.PooledStringBuilder
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.PooledStringBuilder
    name: PooledStringBuilder
    href: DrawnUi.Draw.SkiaLabel.PooledStringBuilder.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.PooledStringBuilder
    name: PooledStringBuilder
    href: DrawnUi.Draw.SkiaLabel.PooledStringBuilder.html
- uid: DrawnUi.Draw.SkiaLabel.SpanMeasurement
  commentId: T:DrawnUi.Draw.SkiaLabel.SpanMeasurement
  href: DrawnUi.Draw.SkiaLabel.html
  name: SkiaLabel.SpanMeasurement
  nameWithType: SkiaLabel.SpanMeasurement
  fullName: DrawnUi.Draw.SkiaLabel.SpanMeasurement
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.SpanMeasurement
    name: SpanMeasurement
    href: DrawnUi.Draw.SkiaLabel.SpanMeasurement.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLabel.SpanMeasurement
    name: SpanMeasurement
    href: DrawnUi.Draw.SkiaLabel.SpanMeasurement.html
- uid: DrawnUi.Draw.StringReference
  commentId: T:DrawnUi.Draw.StringReference
  href: DrawnUi.Draw.StringReference.html
  name: StringReference
  nameWithType: StringReference
  fullName: DrawnUi.Draw.StringReference
- uid: DrawnUi.Draw.SvgSpan
  commentId: T:DrawnUi.Draw.SvgSpan
  href: DrawnUi.Draw.SvgSpan.html
  name: SvgSpan
  nameWithType: SvgSpan
  fullName: DrawnUi.Draw.SvgSpan
- uid: DrawnUi.Draw.TextLine
  commentId: T:DrawnUi.Draw.TextLine
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.TextLine.html
  name: TextLine
  nameWithType: TextLine
  fullName: DrawnUi.Draw.TextLine
- uid: DrawnUi.Draw.TextSpan
  commentId: T:DrawnUi.Draw.TextSpan
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.TextSpan.html
  name: TextSpan
  nameWithType: TextSpan
  fullName: DrawnUi.Draw.TextSpan
- uid: DrawnUi.Draw.UsedGlyph
  commentId: T:DrawnUi.Draw.UsedGlyph
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.UsedGlyph.html
  name: UsedGlyph
  nameWithType: UsedGlyph
  fullName: DrawnUi.Draw.UsedGlyph
- uid: DrawnUi.Draw.DebugImage
  commentId: T:DrawnUi.Draw.DebugImage
  href: DrawnUi.Draw.DebugImage.html
  name: DebugImage
  nameWithType: DebugImage
  fullName: DrawnUi.Draw.DebugImage
- uid: DrawnUi.Draw.AdjustRGBEffect
  commentId: T:DrawnUi.Draw.AdjustRGBEffect
  href: DrawnUi.Draw.AdjustRGBEffect.html
  name: AdjustRGBEffect
  nameWithType: AdjustRGBEffect
  fullName: DrawnUi.Draw.AdjustRGBEffect
- uid: DrawnUi.Draw.ChainAdjustRGBEffect
  commentId: T:DrawnUi.Draw.ChainAdjustRGBEffect
  href: DrawnUi.Draw.ChainAdjustRGBEffect.html
  name: ChainAdjustRGBEffect
  nameWithType: ChainAdjustRGBEffect
  fullName: DrawnUi.Draw.ChainAdjustRGBEffect
- uid: DrawnUi.Draw.BaseColorFilterEffect
  commentId: T:DrawnUi.Draw.BaseColorFilterEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.BaseColorFilterEffect.html
  name: BaseColorFilterEffect
  nameWithType: BaseColorFilterEffect
  fullName: DrawnUi.Draw.BaseColorFilterEffect
- uid: DrawnUi.Draw.BaseImageFilterEffect
  commentId: T:DrawnUi.Draw.BaseImageFilterEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.BaseImageFilterEffect.html
  name: BaseImageFilterEffect
  nameWithType: BaseImageFilterEffect
  fullName: DrawnUi.Draw.BaseImageFilterEffect
- uid: DrawnUi.Draw.BaseChainedEffect
  commentId: T:DrawnUi.Draw.BaseChainedEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.BaseChainedEffect.html
  name: BaseChainedEffect
  nameWithType: BaseChainedEffect
  fullName: DrawnUi.Draw.BaseChainedEffect
- uid: DrawnUi.Draw.BlurEffect
  commentId: T:DrawnUi.Draw.BlurEffect
  href: DrawnUi.Draw.BlurEffect.html
  name: BlurEffect
  nameWithType: BlurEffect
  fullName: DrawnUi.Draw.BlurEffect
- uid: DrawnUi.Draw.ChainAdjustBrightnessEffect
  commentId: T:DrawnUi.Draw.ChainAdjustBrightnessEffect
  href: DrawnUi.Draw.ChainAdjustBrightnessEffect.html
  name: ChainAdjustBrightnessEffect
  nameWithType: ChainAdjustBrightnessEffect
  fullName: DrawnUi.Draw.ChainAdjustBrightnessEffect
- uid: DrawnUi.Draw.ChainAdjustContrastEffect
  commentId: T:DrawnUi.Draw.ChainAdjustContrastEffect
  href: DrawnUi.Draw.ChainAdjustContrastEffect.html
  name: ChainAdjustContrastEffect
  nameWithType: ChainAdjustContrastEffect
  fullName: DrawnUi.Draw.ChainAdjustContrastEffect
- uid: DrawnUi.Draw.AdjustBrightnessEffect
  commentId: T:DrawnUi.Draw.AdjustBrightnessEffect
  href: DrawnUi.Draw.AdjustBrightnessEffect.html
  name: AdjustBrightnessEffect
  nameWithType: AdjustBrightnessEffect
  fullName: DrawnUi.Draw.AdjustBrightnessEffect
- uid: DrawnUi.Draw.ChainAdjustLightnessEffect
  commentId: T:DrawnUi.Draw.ChainAdjustLightnessEffect
  href: DrawnUi.Draw.ChainAdjustLightnessEffect.html
  name: ChainAdjustLightnessEffect
  nameWithType: ChainAdjustLightnessEffect
  fullName: DrawnUi.Draw.ChainAdjustLightnessEffect
- uid: DrawnUi.Draw.ChainColorPresetEffect
  commentId: T:DrawnUi.Draw.ChainColorPresetEffect
  href: DrawnUi.Draw.ChainColorPresetEffect.html
  name: ChainColorPresetEffect
  nameWithType: ChainColorPresetEffect
  fullName: DrawnUi.Draw.ChainColorPresetEffect
- uid: DrawnUi.Draw.ChainDropShadowsEffect
  commentId: T:DrawnUi.Draw.ChainDropShadowsEffect
  href: DrawnUi.Draw.ChainDropShadowsEffect.html
  name: ChainDropShadowsEffect
  nameWithType: ChainDropShadowsEffect
  fullName: DrawnUi.Draw.ChainDropShadowsEffect
- uid: DrawnUi.Draw.ChainEffectResult
  commentId: T:DrawnUi.Draw.ChainEffectResult
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ChainEffectResult.html
  name: ChainEffectResult
  nameWithType: ChainEffectResult
  fullName: DrawnUi.Draw.ChainEffectResult
- uid: DrawnUi.Draw.ChainSaturationEffect
  commentId: T:DrawnUi.Draw.ChainSaturationEffect
  href: DrawnUi.Draw.ChainSaturationEffect.html
  name: ChainSaturationEffect
  nameWithType: ChainSaturationEffect
  fullName: DrawnUi.Draw.ChainSaturationEffect
- uid: DrawnUi.Draw.ChainTintWithAlphaEffect
  commentId: T:DrawnUi.Draw.ChainTintWithAlphaEffect
  href: DrawnUi.Draw.ChainTintWithAlphaEffect.html
  name: ChainTintWithAlphaEffect
  nameWithType: ChainTintWithAlphaEffect
  fullName: DrawnUi.Draw.ChainTintWithAlphaEffect
- uid: DrawnUi.Draw.ColorPresetEffect
  commentId: T:DrawnUi.Draw.ColorPresetEffect
  href: DrawnUi.Draw.ColorPresetEffect.html
  name: ColorPresetEffect
  nameWithType: ColorPresetEffect
  fullName: DrawnUi.Draw.ColorPresetEffect
- uid: DrawnUi.Draw.ContrastEffect
  commentId: T:DrawnUi.Draw.ContrastEffect
  href: DrawnUi.Draw.ContrastEffect.html
  name: ContrastEffect
  nameWithType: ContrastEffect
  fullName: DrawnUi.Draw.ContrastEffect
- uid: DrawnUi.Draw.DropShadowEffect
  commentId: T:DrawnUi.Draw.DropShadowEffect
  href: DrawnUi.Draw.DropShadowEffect.html
  name: DropShadowEffect
  nameWithType: DropShadowEffect
  fullName: DrawnUi.Draw.DropShadowEffect
- uid: DrawnUi.Draw.IColorEffect
  commentId: T:DrawnUi.Draw.IColorEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IColorEffect.html
  name: IColorEffect
  nameWithType: IColorEffect
  fullName: DrawnUi.Draw.IColorEffect
- uid: DrawnUi.Draw.IImageEffect
  commentId: T:DrawnUi.Draw.IImageEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IImageEffect.html
  name: IImageEffect
  nameWithType: IImageEffect
  fullName: DrawnUi.Draw.IImageEffect
- uid: DrawnUi.Draw.IRenderEffect
  commentId: T:DrawnUi.Draw.IRenderEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IRenderEffect.html
  name: IRenderEffect
  nameWithType: IRenderEffect
  fullName: DrawnUi.Draw.IRenderEffect
- uid: DrawnUi.Draw.ISkiaEffect
  commentId: T:DrawnUi.Draw.ISkiaEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaEffect.html
  name: ISkiaEffect
  nameWithType: ISkiaEffect
  fullName: DrawnUi.Draw.ISkiaEffect
- uid: DrawnUi.Draw.IPostRendererEffect
  commentId: T:DrawnUi.Draw.IPostRendererEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IPostRendererEffect.html
  name: IPostRendererEffect
  nameWithType: IPostRendererEffect
  fullName: DrawnUi.Draw.IPostRendererEffect
- uid: DrawnUi.Draw.IStateEffect
  commentId: T:DrawnUi.Draw.IStateEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IStateEffect.html
  name: IStateEffect
  nameWithType: IStateEffect
  fullName: DrawnUi.Draw.IStateEffect
- uid: DrawnUi.Draw.SaturationEffect
  commentId: T:DrawnUi.Draw.SaturationEffect
  href: DrawnUi.Draw.SaturationEffect.html
  name: SaturationEffect
  nameWithType: SaturationEffect
  fullName: DrawnUi.Draw.SaturationEffect
- uid: DrawnUi.Draw.ShaderDoubleTexturesEffect
  commentId: T:DrawnUi.Draw.ShaderDoubleTexturesEffect
  href: DrawnUi.Draw.ShaderDoubleTexturesEffect.html
  name: ShaderDoubleTexturesEffect
  nameWithType: ShaderDoubleTexturesEffect
  fullName: DrawnUi.Draw.ShaderDoubleTexturesEffect
- uid: DrawnUi.Draw.SkiaDoubleAttachedTexturesEffect
  commentId: T:DrawnUi.Draw.SkiaDoubleAttachedTexturesEffect
  href: DrawnUi.Draw.SkiaDoubleAttachedTexturesEffect.html
  name: SkiaDoubleAttachedTexturesEffect
  nameWithType: SkiaDoubleAttachedTexturesEffect
  fullName: DrawnUi.Draw.SkiaDoubleAttachedTexturesEffect
- uid: DrawnUi.Draw.SkiaEffect
  commentId: T:DrawnUi.Draw.SkiaEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaEffect.html
  name: SkiaEffect
  nameWithType: SkiaEffect
  fullName: DrawnUi.Draw.SkiaEffect
- uid: DrawnUi.Draw.SkiaShaderEffect
  commentId: T:DrawnUi.Draw.SkiaShaderEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaShaderEffect.html
  name: SkiaShaderEffect
  nameWithType: SkiaShaderEffect
  fullName: DrawnUi.Draw.SkiaShaderEffect
- uid: DrawnUi.Draw.StateEffect
  commentId: T:DrawnUi.Draw.StateEffect
  href: DrawnUi.Draw.StateEffect.html
  name: StateEffect
  nameWithType: StateEffect
  fullName: DrawnUi.Draw.StateEffect
- uid: DrawnUi.Draw.TintEffect
  commentId: T:DrawnUi.Draw.TintEffect
  href: DrawnUi.Draw.TintEffect.html
  name: TintEffect
  nameWithType: TintEffect
  fullName: DrawnUi.Draw.TintEffect
- uid: DrawnUi.Draw.TintWithAlphaEffect
  commentId: T:DrawnUi.Draw.TintWithAlphaEffect
  href: DrawnUi.Draw.TintWithAlphaEffect.html
  name: TintWithAlphaEffect
  nameWithType: TintWithAlphaEffect
  fullName: DrawnUi.Draw.TintWithAlphaEffect
- uid: DrawnUi.Draw.SkiaFontManager
  commentId: T:DrawnUi.Draw.SkiaFontManager
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaFontManager.html
  name: SkiaFontManager
  nameWithType: SkiaFontManager
  fullName: DrawnUi.Draw.SkiaFontManager
- uid: DrawnUi.Draw.AddGestures
  commentId: T:DrawnUi.Draw.AddGestures
  href: DrawnUi.Draw.AddGestures.html
  name: AddGestures
  nameWithType: AddGestures
  fullName: DrawnUi.Draw.AddGestures
- uid: DrawnUi.Draw.AddGestures.GestureListener
  commentId: T:DrawnUi.Draw.AddGestures.GestureListener
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.AddGestures.html
  name: AddGestures.GestureListener
  nameWithType: AddGestures.GestureListener
  fullName: DrawnUi.Draw.AddGestures.GestureListener
  spec.csharp:
  - uid: DrawnUi.Draw.AddGestures
    name: AddGestures
    href: DrawnUi.Draw.AddGestures.html
  - name: .
  - uid: DrawnUi.Draw.AddGestures.GestureListener
    name: GestureListener
    href: DrawnUi.Draw.AddGestures.GestureListener.html
  spec.vb:
  - uid: DrawnUi.Draw.AddGestures
    name: AddGestures
    href: DrawnUi.Draw.AddGestures.html
  - name: .
  - uid: DrawnUi.Draw.AddGestures.GestureListener
    name: GestureListener
    href: DrawnUi.Draw.AddGestures.GestureListener.html
- uid: DrawnUi.Draw.GesturesMode
  commentId: T:DrawnUi.Draw.GesturesMode
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.GesturesMode.html
  name: GesturesMode
  nameWithType: GesturesMode
  fullName: DrawnUi.Draw.GesturesMode
- uid: DrawnUi.Draw.SkiaGesturesInfo
  commentId: T:DrawnUi.Draw.SkiaGesturesInfo
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaGesturesInfo.html
  name: SkiaGesturesInfo
  nameWithType: SkiaGesturesInfo
  fullName: DrawnUi.Draw.SkiaGesturesInfo
- uid: DrawnUi.Draw.SkiaGesturesParameters
  commentId: T:DrawnUi.Draw.SkiaGesturesParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaGesturesParameters.html
  name: SkiaGesturesParameters
  nameWithType: SkiaGesturesParameters
  fullName: DrawnUi.Draw.SkiaGesturesParameters
- uid: DrawnUi.Draw.GestureEventProcessingInfo
  commentId: T:DrawnUi.Draw.GestureEventProcessingInfo
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.GestureEventProcessingInfo.html
  name: GestureEventProcessingInfo
  nameWithType: GestureEventProcessingInfo
  fullName: DrawnUi.Draw.GestureEventProcessingInfo
- uid: DrawnUi.Draw.ZoomEventArgs
  commentId: T:DrawnUi.Draw.ZoomEventArgs
  href: DrawnUi.Draw.ZoomEventArgs.html
  name: ZoomEventArgs
  nameWithType: ZoomEventArgs
  fullName: DrawnUi.Draw.ZoomEventArgs
- uid: DrawnUi.Draw.IHasBanner
  commentId: T:DrawnUi.Draw.IHasBanner
  href: DrawnUi.Draw.IHasBanner.html
  name: IHasBanner
  nameWithType: IHasBanner
  fullName: DrawnUi.Draw.IHasBanner
- uid: DrawnUi.Draw.LoadPriority
  commentId: T:DrawnUi.Draw.LoadPriority
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LoadPriority.html
  name: LoadPriority
  nameWithType: LoadPriority
  fullName: DrawnUi.Draw.LoadPriority
- uid: DrawnUi.Draw.SkiaImageManager
  commentId: T:DrawnUi.Draw.SkiaImageManager
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaImageManager.html
  name: SkiaImageManager
  nameWithType: SkiaImageManager
  fullName: DrawnUi.Draw.SkiaImageManager
- uid: DrawnUi.Draw.SkiaImageManager.QueueItem
  commentId: T:DrawnUi.Draw.SkiaImageManager.QueueItem
  href: DrawnUi.Draw.SkiaImageManager.html
  name: SkiaImageManager.QueueItem
  nameWithType: SkiaImageManager.QueueItem
  fullName: DrawnUi.Draw.SkiaImageManager.QueueItem
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaImageManager
    name: SkiaImageManager
    href: DrawnUi.Draw.SkiaImageManager.html
  - name: .
  - uid: DrawnUi.Draw.SkiaImageManager.QueueItem
    name: QueueItem
    href: DrawnUi.Draw.SkiaImageManager.QueueItem.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaImageManager
    name: SkiaImageManager
    href: DrawnUi.Draw.SkiaImageManager.html
  - name: .
  - uid: DrawnUi.Draw.SkiaImageManager.QueueItem
    name: QueueItem
    href: DrawnUi.Draw.SkiaImageManager.QueueItem.html
- uid: DrawnUi.Draw.KeyboardManager
  commentId: T:DrawnUi.Draw.KeyboardManager
  href: DrawnUi.Draw.KeyboardManager.html
  name: KeyboardManager
  nameWithType: KeyboardManager
  fullName: DrawnUi.Draw.KeyboardManager
- uid: DrawnUi.Draw.MauiKey
  commentId: T:DrawnUi.Draw.MauiKey
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.MauiKey.html
  name: MauiKey
  nameWithType: MauiKey
  fullName: DrawnUi.Draw.MauiKey
- uid: DrawnUi.Draw.MauiKeyMapper
  commentId: T:DrawnUi.Draw.MauiKeyMapper
  href: DrawnUi.Draw.MauiKeyMapper.html
  name: MauiKeyMapper
  nameWithType: MauiKeyMapper
  fullName: DrawnUi.Draw.MauiKeyMapper
- uid: DrawnUi.Draw.ColorExtensions
  commentId: T:DrawnUi.Draw.ColorExtensions
  href: DrawnUi.Draw.ColorExtensions.html
  name: ColorExtensions
  nameWithType: ColorExtensions
  fullName: DrawnUi.Draw.ColorExtensions
- uid: DrawnUi.Draw.DrawnExtensions
  commentId: T:DrawnUi.Draw.DrawnExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawnExtensions.html
  name: DrawnExtensions
  nameWithType: DrawnExtensions
  fullName: DrawnUi.Draw.DrawnExtensions
- uid: DrawnUi.Draw.FluentExtensions
  commentId: T:DrawnUi.Draw.FluentExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.FluentExtensions.html
  name: FluentExtensions
  nameWithType: FluentExtensions
  fullName: DrawnUi.Draw.FluentExtensions
- uid: DrawnUi.Draw.StaticResourcesExtensions
  commentId: T:DrawnUi.Draw.StaticResourcesExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.StaticResourcesExtensions.html
  name: StaticResourcesExtensions
  nameWithType: StaticResourcesExtensions
  fullName: DrawnUi.Draw.StaticResourcesExtensions
- uid: DrawnUi.Draw.SkiaSetter
  commentId: T:DrawnUi.Draw.SkiaSetter
  href: DrawnUi.Draw.SkiaSetter.html
  name: SkiaSetter
  nameWithType: SkiaSetter
  fullName: DrawnUi.Draw.SkiaSetter
- uid: DrawnUi.Draw.BindablePropertyExtension
  commentId: T:DrawnUi.Draw.BindablePropertyExtension
  href: DrawnUi.Draw.BindablePropertyExtension.html
  name: BindablePropertyExtension
  nameWithType: BindablePropertyExtension
  fullName: DrawnUi.Draw.BindablePropertyExtension
- uid: DrawnUi.Draw.BindToParentContextExtension
  commentId: T:DrawnUi.Draw.BindToParentContextExtension
  href: DrawnUi.Draw.BindToParentContextExtension.html
  name: BindToParentContextExtension
  nameWithType: BindToParentContextExtension
  fullName: DrawnUi.Draw.BindToParentContextExtension
- uid: DrawnUi.Draw.DrawnFontAttributesConverter
  commentId: T:DrawnUi.Draw.DrawnFontAttributesConverter
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawnFontAttributesConverter.html
  name: DrawnFontAttributesConverter
  nameWithType: DrawnFontAttributesConverter
  fullName: DrawnUi.Draw.DrawnFontAttributesConverter
- uid: DrawnUi.Draw.FindTagExtension
  commentId: T:DrawnUi.Draw.FindTagExtension
  href: DrawnUi.Draw.FindTagExtension.html
  name: FindTagExtension
  nameWithType: FindTagExtension
  fullName: DrawnUi.Draw.FindTagExtension
- uid: DrawnUi.Draw.Super
  commentId: T:DrawnUi.Draw.Super
  href: DrawnUi.Draw.Super.html
  name: Super
  nameWithType: Super
  fullName: DrawnUi.Draw.Super
- uid: DrawnUi.Draw.ObservableAttachedItemsCollection`1
  commentId: T:DrawnUi.Draw.ObservableAttachedItemsCollection`1
  href: DrawnUi.Draw.ObservableAttachedItemsCollection-1.html
  name: ObservableAttachedItemsCollection<T>
  nameWithType: ObservableAttachedItemsCollection<T>
  fullName: DrawnUi.Draw.ObservableAttachedItemsCollection<T>
  nameWithType.vb: ObservableAttachedItemsCollection(Of T)
  fullName.vb: DrawnUi.Draw.ObservableAttachedItemsCollection(Of T)
  name.vb: ObservableAttachedItemsCollection(Of T)
  spec.csharp:
  - uid: DrawnUi.Draw.ObservableAttachedItemsCollection`1
    name: ObservableAttachedItemsCollection
    href: DrawnUi.Draw.ObservableAttachedItemsCollection-1.html
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: DrawnUi.Draw.ObservableAttachedItemsCollection`1
    name: ObservableAttachedItemsCollection
    href: DrawnUi.Draw.ObservableAttachedItemsCollection-1.html
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.VisualLayer
  commentId: T:DrawnUi.Draw.VisualLayer
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.VisualLayer.html
  name: VisualLayer
  nameWithType: VisualLayer
  fullName: DrawnUi.Draw.VisualLayer
- uid: DrawnUi.Draw.Snapping
  commentId: T:DrawnUi.Draw.Snapping
  href: DrawnUi.Draw.Snapping.html
  name: Snapping
  nameWithType: Snapping
  fullName: DrawnUi.Draw.Snapping
- uid: DrawnUi.Draw.VisualTreeHandler
  commentId: T:DrawnUi.Draw.VisualTreeHandler
  href: DrawnUi.Draw.VisualTreeHandler.html
  name: VisualTreeHandler
  nameWithType: VisualTreeHandler
  fullName: DrawnUi.Draw.VisualTreeHandler
- uid: DrawnUi.Draw.SkiaCacheType
  commentId: T:DrawnUi.Draw.SkiaCacheType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaCacheType.html
  name: SkiaCacheType
  nameWithType: SkiaCacheType
  fullName: DrawnUi.Draw.SkiaCacheType
- uid: DrawnUi.Draw.CachedObject
  commentId: T:DrawnUi.Draw.CachedObject
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.CachedObject.html
  name: CachedObject
  nameWithType: CachedObject
  fullName: DrawnUi.Draw.CachedObject
- uid: DrawnUi.Draw.AutoSizeType
  commentId: T:DrawnUi.Draw.AutoSizeType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.AutoSizeType.html
  name: AutoSizeType
  nameWithType: AutoSizeType
  fullName: DrawnUi.Draw.AutoSizeType
- uid: DrawnUi.Draw.DirectionType
  commentId: T:DrawnUi.Draw.DirectionType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DirectionType.html
  name: DirectionType
  nameWithType: DirectionType
  fullName: DrawnUi.Draw.DirectionType
- uid: DrawnUi.Draw.DrawImageAlignment
  commentId: T:DrawnUi.Draw.DrawImageAlignment
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawImageAlignment.html
  name: DrawImageAlignment
  nameWithType: DrawImageAlignment
  fullName: DrawnUi.Draw.DrawImageAlignment
- uid: DrawnUi.Draw.DrawTextAlignment
  commentId: T:DrawnUi.Draw.DrawTextAlignment
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawTextAlignment.html
  name: DrawTextAlignment
  nameWithType: DrawTextAlignment
  fullName: DrawnUi.Draw.DrawTextAlignment
- uid: DrawnUi.Draw.FontWeight
  commentId: T:DrawnUi.Draw.FontWeight
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.FontWeight.html
  name: FontWeight
  nameWithType: FontWeight
  fullName: DrawnUi.Draw.FontWeight
- uid: DrawnUi.Draw.GradientType
  commentId: T:DrawnUi.Draw.GradientType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.GradientType.html
  name: GradientType
  nameWithType: GradientType
  fullName: DrawnUi.Draw.GradientType
- uid: DrawnUi.Draw.RenderingModeType
  commentId: T:DrawnUi.Draw.RenderingModeType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RenderingModeType.html
  name: RenderingModeType
  nameWithType: RenderingModeType
  fullName: DrawnUi.Draw.RenderingModeType
- uid: DrawnUi.Draw.LayoutType
  commentId: T:DrawnUi.Draw.LayoutType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LayoutType.html
  name: LayoutType
  nameWithType: LayoutType
  fullName: DrawnUi.Draw.LayoutType
- uid: DrawnUi.Draw.LockTouch
  commentId: T:DrawnUi.Draw.LockTouch
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LockTouch.html
  name: LockTouch
  nameWithType: LockTouch
  fullName: DrawnUi.Draw.LockTouch
- uid: DrawnUi.Draw.MeasuringStrategy
  commentId: T:DrawnUi.Draw.MeasuringStrategy
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.MeasuringStrategy.html
  name: MeasuringStrategy
  nameWithType: MeasuringStrategy
  fullName: DrawnUi.Draw.MeasuringStrategy
- uid: DrawnUi.Draw.ObjectAliveType
  commentId: T:DrawnUi.Draw.ObjectAliveType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ObjectAliveType.html
  name: ObjectAliveType
  nameWithType: ObjectAliveType
  fullName: DrawnUi.Draw.ObjectAliveType
- uid: DrawnUi.Draw.OrientationType
  commentId: T:DrawnUi.Draw.OrientationType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.OrientationType.html
  name: OrientationType
  nameWithType: OrientationType
  fullName: DrawnUi.Draw.OrientationType
- uid: DrawnUi.Draw.PanningModeType
  commentId: T:DrawnUi.Draw.PanningModeType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.PanningModeType.html
  name: PanningModeType
  nameWithType: PanningModeType
  fullName: DrawnUi.Draw.PanningModeType
- uid: DrawnUi.Draw.PointedDirectionType
  commentId: T:DrawnUi.Draw.PointedDirectionType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.PointedDirectionType.html
  name: PointedDirectionType
  nameWithType: PointedDirectionType
  fullName: DrawnUi.Draw.PointedDirectionType
- uid: DrawnUi.Draw.LinearDirectionType
  commentId: T:DrawnUi.Draw.LinearDirectionType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LinearDirectionType.html
  name: LinearDirectionType
  nameWithType: LinearDirectionType
  fullName: DrawnUi.Draw.LinearDirectionType
- uid: DrawnUi.Draw.RangeZone
  commentId: T:DrawnUi.Draw.RangeZone
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RangeZone.html
  name: RangeZone
  nameWithType: RangeZone
  fullName: DrawnUi.Draw.RangeZone
- uid: DrawnUi.Draw.RecycleTemplateType
  commentId: T:DrawnUi.Draw.RecycleTemplateType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RecycleTemplateType.html
  name: RecycleTemplateType
  nameWithType: RecycleTemplateType
  fullName: DrawnUi.Draw.RecycleTemplateType
- uid: DrawnUi.Draw.RecyclingTemplate
  commentId: T:DrawnUi.Draw.RecyclingTemplate
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RecyclingTemplate.html
  name: RecyclingTemplate
  nameWithType: RecyclingTemplate
  fullName: DrawnUi.Draw.RecyclingTemplate
- uid: DrawnUi.Draw.RelativePositionType
  commentId: T:DrawnUi.Draw.RelativePositionType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RelativePositionType.html
  name: RelativePositionType
  nameWithType: RelativePositionType
  fullName: DrawnUi.Draw.RelativePositionType
- uid: DrawnUi.Draw.ScrollInteractionState
  commentId: T:DrawnUi.Draw.ScrollInteractionState
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScrollInteractionState.html
  name: ScrollInteractionState
  nameWithType: ScrollInteractionState
  fullName: DrawnUi.Draw.ScrollInteractionState
- uid: DrawnUi.Draw.SidePosition
  commentId: T:DrawnUi.Draw.SidePosition
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SidePosition.html
  name: SidePosition
  nameWithType: SidePosition
  fullName: DrawnUi.Draw.SidePosition
- uid: DrawnUi.Draw.ShapeType
  commentId: T:DrawnUi.Draw.ShapeType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ShapeType.html
  name: ShapeType
  nameWithType: ShapeType
  fullName: DrawnUi.Draw.ShapeType
- uid: DrawnUi.Draw.SkiaAnchorBak
  commentId: T:DrawnUi.Draw.SkiaAnchorBak
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaAnchorBak.html
  name: SkiaAnchorBak
  nameWithType: SkiaAnchorBak
  fullName: DrawnUi.Draw.SkiaAnchorBak
- uid: DrawnUi.Draw.SkiaImageEffect
  commentId: T:DrawnUi.Draw.SkiaImageEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaImageEffect.html
  name: SkiaImageEffect
  nameWithType: SkiaImageEffect
  fullName: DrawnUi.Draw.SkiaImageEffect
- uid: DrawnUi.Draw.SkiaImageEffects
  commentId: T:DrawnUi.Draw.SkiaImageEffects
  href: DrawnUi.Draw.SkiaImageEffects.html
  name: SkiaImageEffects
  nameWithType: SkiaImageEffects
  fullName: DrawnUi.Draw.SkiaImageEffects
- uid: DrawnUi.Draw.SkiaTouchAnimation
  commentId: T:DrawnUi.Draw.SkiaTouchAnimation
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaTouchAnimation.html
  name: SkiaTouchAnimation
  nameWithType: SkiaTouchAnimation
  fullName: DrawnUi.Draw.SkiaTouchAnimation
- uid: DrawnUi.Draw.SnapToChildrenType
  commentId: T:DrawnUi.Draw.SnapToChildrenType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SnapToChildrenType.html
  name: SnapToChildrenType
  nameWithType: SnapToChildrenType
  fullName: DrawnUi.Draw.SnapToChildrenType
- uid: DrawnUi.Draw.SourceType
  commentId: T:DrawnUi.Draw.SourceType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SourceType.html
  name: SourceType
  nameWithType: SourceType
  fullName: DrawnUi.Draw.SourceType
- uid: DrawnUi.Draw.SpaceDistribution
  commentId: T:DrawnUi.Draw.SpaceDistribution
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SpaceDistribution.html
  name: SpaceDistribution
  nameWithType: SpaceDistribution
  fullName: DrawnUi.Draw.SpaceDistribution
- uid: DrawnUi.Draw.TextTransform
  commentId: T:DrawnUi.Draw.TextTransform
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.TextTransform.html
  name: TextTransform
  nameWithType: TextTransform
  fullName: DrawnUi.Draw.TextTransform
- uid: DrawnUi.Draw.TransformAspect
  commentId: T:DrawnUi.Draw.TransformAspect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.TransformAspect.html
  name: TransformAspect
  nameWithType: TransformAspect
  fullName: DrawnUi.Draw.TransformAspect
- uid: DrawnUi.Draw.ViewportScrollType
  commentId: T:DrawnUi.Draw.ViewportScrollType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ViewportScrollType.html
  name: ViewportScrollType
  nameWithType: ViewportScrollType
  fullName: DrawnUi.Draw.ViewportScrollType
- uid: DrawnUi.Draw.VirtualisationType
  commentId: T:DrawnUi.Draw.VirtualisationType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.VirtualisationType.html
  name: VirtualisationType
  nameWithType: VirtualisationType
  fullName: DrawnUi.Draw.VirtualisationType
- uid: DrawnUi.Draw.Looper
  commentId: T:DrawnUi.Draw.Looper
  href: DrawnUi.Draw.Looper.html
  name: Looper
  nameWithType: Looper
  fullName: DrawnUi.Draw.Looper
- uid: DrawnUi.Draw.IAnimatorsManager
  commentId: T:DrawnUi.Draw.IAnimatorsManager
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IAnimatorsManager.html
  name: IAnimatorsManager
  nameWithType: IAnimatorsManager
  fullName: DrawnUi.Draw.IAnimatorsManager
- uid: DrawnUi.Draw.IBindingContextDebuggable
  commentId: T:DrawnUi.Draw.IBindingContextDebuggable
  href: DrawnUi.Draw.IBindingContextDebuggable.html
  name: IBindingContextDebuggable
  nameWithType: IBindingContextDebuggable
  fullName: DrawnUi.Draw.IBindingContextDebuggable
- uid: DrawnUi.Draw.ICanBeUpdated
  commentId: T:DrawnUi.Draw.ICanBeUpdated
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ICanBeUpdated.html
  name: ICanBeUpdated
  nameWithType: ICanBeUpdated
  fullName: DrawnUi.Draw.ICanBeUpdated
- uid: DrawnUi.Draw.ICanBeUpdatedWithContext
  commentId: T:DrawnUi.Draw.ICanBeUpdatedWithContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ICanBeUpdatedWithContext.html
  name: ICanBeUpdatedWithContext
  nameWithType: ICanBeUpdatedWithContext
  fullName: DrawnUi.Draw.ICanBeUpdatedWithContext
- uid: DrawnUi.Draw.IDefinesViewport
  commentId: T:DrawnUi.Draw.IDefinesViewport
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDefinesViewport.html
  name: IDefinesViewport
  nameWithType: IDefinesViewport
  fullName: DrawnUi.Draw.IDefinesViewport
- uid: DrawnUi.Draw.IDrawnBase
  commentId: T:DrawnUi.Draw.IDrawnBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDrawnBase.html
  name: IDrawnBase
  nameWithType: IDrawnBase
  fullName: DrawnUi.Draw.IDrawnBase
- uid: DrawnUi.Draw.IHasAfterEffects
  commentId: T:DrawnUi.Draw.IHasAfterEffects
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IHasAfterEffects.html
  name: IHasAfterEffects
  nameWithType: IHasAfterEffects
  fullName: DrawnUi.Draw.IHasAfterEffects
- uid: DrawnUi.Draw.IInsideViewport
  commentId: T:DrawnUi.Draw.IInsideViewport
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IInsideViewport.html
  name: IInsideViewport
  nameWithType: IInsideViewport
  fullName: DrawnUi.Draw.IInsideViewport
- uid: DrawnUi.Draw.IInsideWheelStack
  commentId: T:DrawnUi.Draw.IInsideWheelStack
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IInsideWheelStack.html
  name: IInsideWheelStack
  nameWithType: IInsideWheelStack
  fullName: DrawnUi.Draw.IInsideWheelStack
- uid: DrawnUi.Draw.ILayoutInsideViewport
  commentId: T:DrawnUi.Draw.ILayoutInsideViewport
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ILayoutInsideViewport.html
  name: ILayoutInsideViewport
  nameWithType: ILayoutInsideViewport
  fullName: DrawnUi.Draw.ILayoutInsideViewport
- uid: DrawnUi.Draw.IRefreshIndicator
  commentId: T:DrawnUi.Draw.IRefreshIndicator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IRefreshIndicator.html
  name: IRefreshIndicator
  nameWithType: IRefreshIndicator
  fullName: DrawnUi.Draw.IRefreshIndicator
- uid: DrawnUi.Draw.ISkiaCell
  commentId: T:DrawnUi.Draw.ISkiaCell
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaCell.html
  name: ISkiaCell
  nameWithType: ISkiaCell
  fullName: DrawnUi.Draw.ISkiaCell
- uid: DrawnUi.Draw.ISkiaControl
  commentId: T:DrawnUi.Draw.ISkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaControl.html
  name: ISkiaControl
  nameWithType: ISkiaControl
  fullName: DrawnUi.Draw.ISkiaControl
- uid: DrawnUi.Draw.ISkiaDrawable
  commentId: T:DrawnUi.Draw.ISkiaDrawable
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaDrawable.html
  name: ISkiaDrawable
  nameWithType: ISkiaDrawable
  fullName: DrawnUi.Draw.ISkiaDrawable
- uid: DrawnUi.Draw.ISkiaGestureListener
  commentId: T:DrawnUi.Draw.ISkiaGestureListener
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaGestureListener.html
  name: ISkiaGestureListener
  nameWithType: ISkiaGestureListener
  fullName: DrawnUi.Draw.ISkiaGestureListener
- uid: DrawnUi.Draw.ISkiaGestureProcessor
  commentId: T:DrawnUi.Draw.ISkiaGestureProcessor
  href: DrawnUi.Draw.ISkiaGestureProcessor.html
  name: ISkiaGestureProcessor
  nameWithType: ISkiaGestureProcessor
  fullName: DrawnUi.Draw.ISkiaGestureProcessor
- uid: DrawnUi.Draw.ISkiaGridLayout
  commentId: T:DrawnUi.Draw.ISkiaGridLayout
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaGridLayout.html
  name: ISkiaGridLayout
  nameWithType: ISkiaGridLayout
  fullName: DrawnUi.Draw.ISkiaGridLayout
- uid: DrawnUi.Draw.ISkiaLayer
  commentId: T:DrawnUi.Draw.ISkiaLayer
  href: DrawnUi.Draw.ISkiaLayer.html
  name: ISkiaLayer
  nameWithType: ISkiaLayer
  fullName: DrawnUi.Draw.ISkiaLayer
- uid: DrawnUi.Draw.ISkiaLayout
  commentId: T:DrawnUi.Draw.ISkiaLayout
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaLayout.html
  name: ISkiaLayout
  nameWithType: ISkiaLayout
  fullName: DrawnUi.Draw.ISkiaLayout
- uid: DrawnUi.Draw.ISkiaSharpView
  commentId: T:DrawnUi.Draw.ISkiaSharpView
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaSharpView.html
  name: ISkiaSharpView
  nameWithType: ISkiaSharpView
  fullName: DrawnUi.Draw.ISkiaSharpView
- uid: DrawnUi.Draw.IVisibilityAware
  commentId: T:DrawnUi.Draw.IVisibilityAware
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IVisibilityAware.html
  name: IVisibilityAware
  nameWithType: IVisibilityAware
  fullName: DrawnUi.Draw.IVisibilityAware
- uid: DrawnUi.Draw.IWithContent
  commentId: T:DrawnUi.Draw.IWithContent
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IWithContent.html
  name: IWithContent
  nameWithType: IWithContent
  fullName: DrawnUi.Draw.IWithContent
- uid: DrawnUi.Draw.CachedGradient
  commentId: T:DrawnUi.Draw.CachedGradient
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.CachedGradient.html
  name: CachedGradient
  nameWithType: CachedGradient
  fullName: DrawnUi.Draw.CachedGradient
- uid: DrawnUi.Draw.CachedShader
  commentId: T:DrawnUi.Draw.CachedShader
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.CachedShader.html
  name: CachedShader
  nameWithType: CachedShader
  fullName: DrawnUi.Draw.CachedShader
- uid: DrawnUi.Draw.CachedShadow
  commentId: T:DrawnUi.Draw.CachedShadow
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.CachedShadow.html
  name: CachedShadow
  nameWithType: CachedShadow
  fullName: DrawnUi.Draw.CachedShadow
- uid: DrawnUi.Draw.ContainsPointResult
  commentId: T:DrawnUi.Draw.ContainsPointResult
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ContainsPointResult.html
  name: ContainsPointResult
  nameWithType: ContainsPointResult
  fullName: DrawnUi.Draw.ContainsPointResult
- uid: DrawnUi.Draw.ControlInStack
  commentId: T:DrawnUi.Draw.ControlInStack
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ControlInStack.html
  name: ControlInStack
  nameWithType: ControlInStack
  fullName: DrawnUi.Draw.ControlInStack
- uid: DrawnUi.Draw.ControlsTracker
  commentId: T:DrawnUi.Draw.ControlsTracker
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ControlsTracker.html
  name: ControlsTracker
  nameWithType: ControlsTracker
  fullName: DrawnUi.Draw.ControlsTracker
- uid: DrawnUi.Draw.DescendingZIndexGestureListenerComparer
  commentId: T:DrawnUi.Draw.DescendingZIndexGestureListenerComparer
  href: DrawnUi.Draw.DescendingZIndexGestureListenerComparer.html
  name: DescendingZIndexGestureListenerComparer
  nameWithType: DescendingZIndexGestureListenerComparer
  fullName: DrawnUi.Draw.DescendingZIndexGestureListenerComparer
- uid: DrawnUi.Draw.DrawingContext
  commentId: T:DrawnUi.Draw.DrawingContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawingContext.html
  name: DrawingContext
  nameWithType: DrawingContext
  fullName: DrawnUi.Draw.DrawingContext
- uid: DrawnUi.Draw.RenderDrawingContext
  commentId: T:DrawnUi.Draw.RenderDrawingContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RenderDrawingContext.html
  name: RenderDrawingContext
  nameWithType: RenderDrawingContext
  fullName: DrawnUi.Draw.RenderDrawingContext
- uid: DrawnUi.Draw.ContextArguments
  commentId: T:DrawnUi.Draw.ContextArguments
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ContextArguments.html
  name: ContextArguments
  nameWithType: ContextArguments
  fullName: DrawnUi.Draw.ContextArguments
- uid: DrawnUi.Draw.SkiaDrawingContext
  commentId: T:DrawnUi.Draw.SkiaDrawingContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaDrawingContext.html
  name: SkiaDrawingContext
  nameWithType: SkiaDrawingContext
  fullName: DrawnUi.Draw.SkiaDrawingContext
- uid: DrawnUi.Draw.DrawnUiStartupSettings
  commentId: T:DrawnUi.Draw.DrawnUiStartupSettings
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawnUiStartupSettings.html
  name: DrawnUiStartupSettings
  nameWithType: DrawnUiStartupSettings
  fullName: DrawnUi.Draw.DrawnUiStartupSettings
- uid: DrawnUi.Draw.DynamicGrid`1
  commentId: T:DrawnUi.Draw.DynamicGrid`1
  href: DrawnUi.Draw.DynamicGrid-1.html
  name: DynamicGrid<T>
  nameWithType: DynamicGrid<T>
  fullName: DrawnUi.Draw.DynamicGrid<T>
  nameWithType.vb: DynamicGrid(Of T)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T)
  name.vb: DynamicGrid(Of T)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1
    name: DynamicGrid
    href: DrawnUi.Draw.DynamicGrid-1.html
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1
    name: DynamicGrid
    href: DrawnUi.Draw.DynamicGrid-1.html
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.MeasureRequest
  commentId: T:DrawnUi.Draw.MeasureRequest
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.MeasureRequest.html
  name: MeasureRequest
  nameWithType: MeasureRequest
  fullName: DrawnUi.Draw.MeasureRequest
- uid: DrawnUi.Draw.PointIsInsideResult
  commentId: T:DrawnUi.Draw.PointIsInsideResult
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.PointIsInsideResult.html
  name: PointIsInsideResult
  nameWithType: PointIsInsideResult
  fullName: DrawnUi.Draw.PointIsInsideResult
- uid: DrawnUi.Draw.DrawingRect
  commentId: T:DrawnUi.Draw.DrawingRect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawingRect.html
  name: DrawingRect
  nameWithType: DrawingRect
  fullName: DrawnUi.Draw.DrawingRect
- uid: DrawnUi.Draw.ScaledPoint
  commentId: T:DrawnUi.Draw.ScaledPoint
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScaledPoint.html
  name: ScaledPoint
  nameWithType: ScaledPoint
  fullName: DrawnUi.Draw.ScaledPoint
- uid: DrawnUi.Draw.ScaledRect
  commentId: T:DrawnUi.Draw.ScaledRect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScaledRect.html
  name: ScaledRect
  nameWithType: ScaledRect
  fullName: DrawnUi.Draw.ScaledRect
- uid: DrawnUi.Draw.RangeF
  commentId: T:DrawnUi.Draw.RangeF
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RangeF.html
  name: RangeF
  nameWithType: RangeF
  fullName: DrawnUi.Draw.RangeF
- uid: DrawnUi.Draw.ScaledSize
  commentId: T:DrawnUi.Draw.ScaledSize
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScaledSize.html
  name: ScaledSize
  nameWithType: ScaledSize
  fullName: DrawnUi.Draw.ScaledSize
- uid: DrawnUi.Draw.SortedGestureListeners
  commentId: T:DrawnUi.Draw.SortedGestureListeners
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SortedGestureListeners.html
  name: SortedGestureListeners
  nameWithType: SortedGestureListeners
  fullName: DrawnUi.Draw.SortedGestureListeners
- uid: DrawnUi.Draw.WindowParameters
  commentId: T:DrawnUi.Draw.WindowParameters
  href: DrawnUi.Draw.WindowParameters.html
  name: WindowParameters
  nameWithType: WindowParameters
  fullName: DrawnUi.Draw.WindowParameters
- uid: DrawnUi.Draw.BevelType
  commentId: T:DrawnUi.Draw.BevelType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.BevelType.html
  name: BevelType
  nameWithType: BevelType
  fullName: DrawnUi.Draw.BevelType
- uid: DrawnUi.Draw.SkiaBevel
  commentId: T:DrawnUi.Draw.SkiaBevel
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaBevel.html
  name: SkiaBevel
  nameWithType: SkiaBevel
  fullName: DrawnUi.Draw.SkiaBevel
- uid: DrawnUi.Draw.SkiaGradient
  commentId: T:DrawnUi.Draw.SkiaGradient
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaGradient.html
  name: SkiaGradient
  nameWithType: SkiaGradient
  fullName: DrawnUi.Draw.SkiaGradient
- uid: DrawnUi.Draw.SkiaPoint
  commentId: T:DrawnUi.Draw.SkiaPoint
  href: DrawnUi.Draw.SkiaPoint.html
  name: SkiaPoint
  nameWithType: SkiaPoint
  fullName: DrawnUi.Draw.SkiaPoint
- uid: DrawnUi.Draw.SkiaShadow
  commentId: T:DrawnUi.Draw.SkiaShadow
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaShadow.html
  name: SkiaShadow
  nameWithType: SkiaShadow
  fullName: DrawnUi.Draw.SkiaShadow
- uid: DrawnUi.Draw.ActionOnTickAnimator
  commentId: T:DrawnUi.Draw.ActionOnTickAnimator
  href: DrawnUi.Draw.ActionOnTickAnimator.html
  name: ActionOnTickAnimator
  nameWithType: ActionOnTickAnimator
  fullName: DrawnUi.Draw.ActionOnTickAnimator
- uid: DrawnUi.Draw.AnimateExtensions
  commentId: T:DrawnUi.Draw.AnimateExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.AnimateExtensions.html
  name: AnimateExtensions
  nameWithType: AnimateExtensions
  fullName: DrawnUi.Draw.AnimateExtensions
- uid: DrawnUi.Draw.SpringExtensions
  commentId: T:DrawnUi.Draw.SpringExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SpringExtensions.html
  name: SpringExtensions
  nameWithType: SpringExtensions
  fullName: DrawnUi.Draw.SpringExtensions
- uid: DrawnUi.Draw.RippleAnimator
  commentId: T:DrawnUi.Draw.RippleAnimator
  href: DrawnUi.Draw.RippleAnimator.html
  name: RippleAnimator
  nameWithType: RippleAnimator
  fullName: DrawnUi.Draw.RippleAnimator
- uid: DrawnUi.Draw.ShimmerAnimator
  commentId: T:DrawnUi.Draw.ShimmerAnimator
  href: DrawnUi.Draw.ShimmerAnimator.html
  name: ShimmerAnimator
  nameWithType: ShimmerAnimator
  fullName: DrawnUi.Draw.ShimmerAnimator
- uid: DrawnUi.Draw.IAfterEffectDelete
  commentId: T:DrawnUi.Draw.IAfterEffectDelete
  href: DrawnUi.Draw.IAfterEffectDelete.html
  name: IAfterEffectDelete
  nameWithType: IAfterEffectDelete
  fullName: DrawnUi.Draw.IAfterEffectDelete
- uid: DrawnUi.Draw.ICanRenderOnCanvas
  commentId: T:DrawnUi.Draw.ICanRenderOnCanvas
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ICanRenderOnCanvas.html
  name: ICanRenderOnCanvas
  nameWithType: ICanRenderOnCanvas
  fullName: DrawnUi.Draw.ICanRenderOnCanvas
- uid: DrawnUi.Draw.IOverlayEffect
  commentId: T:DrawnUi.Draw.IOverlayEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IOverlayEffect.html
  name: IOverlayEffect
  nameWithType: IOverlayEffect
  fullName: DrawnUi.Draw.IOverlayEffect
- uid: DrawnUi.Draw.IDampingTimingParameters
  commentId: T:DrawnUi.Draw.IDampingTimingParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDampingTimingParameters.html
  name: IDampingTimingParameters
  nameWithType: IDampingTimingParameters
  fullName: DrawnUi.Draw.IDampingTimingParameters
- uid: DrawnUi.Draw.IDampingTimingVectorParameters
  commentId: T:DrawnUi.Draw.IDampingTimingVectorParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDampingTimingVectorParameters.html
  name: IDampingTimingVectorParameters
  nameWithType: IDampingTimingVectorParameters
  fullName: DrawnUi.Draw.IDampingTimingVectorParameters
- uid: DrawnUi.Draw.ISkiaAnimator
  commentId: T:DrawnUi.Draw.ISkiaAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaAnimator.html
  name: ISkiaAnimator
  nameWithType: ISkiaAnimator
  fullName: DrawnUi.Draw.ISkiaAnimator
- uid: DrawnUi.Draw.CriticallyDampedSpringTimingVectorParameters
  commentId: T:DrawnUi.Draw.CriticallyDampedSpringTimingVectorParameters
  href: DrawnUi.Draw.CriticallyDampedSpringTimingVectorParameters.html
  name: CriticallyDampedSpringTimingVectorParameters
  nameWithType: CriticallyDampedSpringTimingVectorParameters
  fullName: DrawnUi.Draw.CriticallyDampedSpringTimingVectorParameters
- uid: DrawnUi.Draw.DecelerationTimingParameters
  commentId: T:DrawnUi.Draw.DecelerationTimingParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DecelerationTimingParameters.html
  name: DecelerationTimingParameters
  nameWithType: DecelerationTimingParameters
  fullName: DrawnUi.Draw.DecelerationTimingParameters
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters
  commentId: T:DrawnUi.Draw.DecelerationTimingVectorParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html
  name: DecelerationTimingVectorParameters
  nameWithType: DecelerationTimingVectorParameters
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters
- uid: DrawnUi.Draw.ITimingVectorParameters
  commentId: T:DrawnUi.Draw.ITimingVectorParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ITimingVectorParameters.html
  name: ITimingVectorParameters
  nameWithType: ITimingVectorParameters
  fullName: DrawnUi.Draw.ITimingVectorParameters
- uid: DrawnUi.Draw.SpringTimingParameters
  commentId: T:DrawnUi.Draw.SpringTimingParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SpringTimingParameters.html
  name: SpringTimingParameters
  nameWithType: SpringTimingParameters
  fullName: DrawnUi.Draw.SpringTimingParameters
- uid: DrawnUi.Draw.CriticallyDampedSpringTimingParameters
  commentId: T:DrawnUi.Draw.CriticallyDampedSpringTimingParameters
  href: DrawnUi.Draw.CriticallyDampedSpringTimingParameters.html
  name: CriticallyDampedSpringTimingParameters
  nameWithType: CriticallyDampedSpringTimingParameters
  fullName: DrawnUi.Draw.CriticallyDampedSpringTimingParameters
- uid: DrawnUi.Draw.UnderdampedSpringTimingParameters
  commentId: T:DrawnUi.Draw.UnderdampedSpringTimingParameters
  href: DrawnUi.Draw.UnderdampedSpringTimingParameters.html
  name: UnderdampedSpringTimingParameters
  nameWithType: UnderdampedSpringTimingParameters
  fullName: DrawnUi.Draw.UnderdampedSpringTimingParameters
- uid: DrawnUi.Draw.SpringTimingVectorParameters
  commentId: T:DrawnUi.Draw.SpringTimingVectorParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SpringTimingVectorParameters.html
  name: SpringTimingVectorParameters
  nameWithType: SpringTimingVectorParameters
  fullName: DrawnUi.Draw.SpringTimingVectorParameters
- uid: DrawnUi.Draw.ITimingParameters
  commentId: T:DrawnUi.Draw.ITimingParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ITimingParameters.html
  name: ITimingParameters
  nameWithType: ITimingParameters
  fullName: DrawnUi.Draw.ITimingParameters
- uid: DrawnUi.Draw.UnderdampedSpringTimingVectorParameters
  commentId: T:DrawnUi.Draw.UnderdampedSpringTimingVectorParameters
  href: DrawnUi.Draw.UnderdampedSpringTimingVectorParameters.html
  name: UnderdampedSpringTimingVectorParameters
  nameWithType: UnderdampedSpringTimingVectorParameters
  fullName: DrawnUi.Draw.UnderdampedSpringTimingVectorParameters
- uid: DrawnUi.Draw.FrameTimeInterpolator
  commentId: T:DrawnUi.Draw.FrameTimeInterpolator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.FrameTimeInterpolator.html
  name: FrameTimeInterpolator
  nameWithType: FrameTimeInterpolator
  fullName: DrawnUi.Draw.FrameTimeInterpolator
- uid: DrawnUi.Draw.IInterpolator
  commentId: T:DrawnUi.Draw.IInterpolator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IInterpolator.html
  name: IInterpolator
  nameWithType: IInterpolator
  fullName: DrawnUi.Draw.IInterpolator
- uid: DrawnUi.Draw.ViscousFluidInterpolator
  commentId: T:DrawnUi.Draw.ViscousFluidInterpolator
  href: DrawnUi.Draw.ViscousFluidInterpolator.html
  name: ViscousFluidInterpolator
  nameWithType: ViscousFluidInterpolator
  fullName: DrawnUi.Draw.ViscousFluidInterpolator
- uid: DrawnUi.Draw.AnimatorBase
  commentId: T:DrawnUi.Draw.AnimatorBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.AnimatorBase.html
  name: AnimatorBase
  nameWithType: AnimatorBase
  fullName: DrawnUi.Draw.AnimatorBase
- uid: DrawnUi.Draw.BlinkAnimator
  commentId: T:DrawnUi.Draw.BlinkAnimator
  href: DrawnUi.Draw.BlinkAnimator.html
  name: BlinkAnimator
  nameWithType: BlinkAnimator
  fullName: DrawnUi.Draw.BlinkAnimator
- uid: DrawnUi.Draw.ToggleAnimator
  commentId: T:DrawnUi.Draw.ToggleAnimator
  href: DrawnUi.Draw.ToggleAnimator.html
  name: ToggleAnimator
  nameWithType: ToggleAnimator
  fullName: DrawnUi.Draw.ToggleAnimator
- uid: DrawnUi.Draw.ColorBlendAnimator
  commentId: T:DrawnUi.Draw.ColorBlendAnimator
  href: DrawnUi.Draw.ColorBlendAnimator.html
  name: ColorBlendAnimator
  nameWithType: ColorBlendAnimator
  fullName: DrawnUi.Draw.ColorBlendAnimator
- uid: DrawnUi.Draw.GlowPosition
  commentId: T:DrawnUi.Draw.GlowPosition
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.GlowPosition.html
  name: GlowPosition
  nameWithType: GlowPosition
  fullName: DrawnUi.Draw.GlowPosition
- uid: DrawnUi.Draw.EdgeGlowAnimator
  commentId: T:DrawnUi.Draw.EdgeGlowAnimator
  href: DrawnUi.Draw.EdgeGlowAnimator.html
  name: EdgeGlowAnimator
  nameWithType: EdgeGlowAnimator
  fullName: DrawnUi.Draw.EdgeGlowAnimator
- uid: DrawnUi.Draw.PendulumAnimator
  commentId: T:DrawnUi.Draw.PendulumAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.PendulumAnimator.html
  name: PendulumAnimator
  nameWithType: PendulumAnimator
  fullName: DrawnUi.Draw.PendulumAnimator
- uid: DrawnUi.Draw.PerpetualPendulumAnimator
  commentId: T:DrawnUi.Draw.PerpetualPendulumAnimator
  href: DrawnUi.Draw.PerpetualPendulumAnimator.html
  name: PerpetualPendulumAnimator
  nameWithType: PerpetualPendulumAnimator
  fullName: DrawnUi.Draw.PerpetualPendulumAnimator
- uid: DrawnUi.Draw.PingPongAnimator
  commentId: T:DrawnUi.Draw.PingPongAnimator
  href: DrawnUi.Draw.PingPongAnimator.html
  name: PingPongAnimator
  nameWithType: PingPongAnimator
  fullName: DrawnUi.Draw.PingPongAnimator
- uid: DrawnUi.Draw.ProgressAnimator
  commentId: T:DrawnUi.Draw.ProgressAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ProgressAnimator.html
  name: ProgressAnimator
  nameWithType: ProgressAnimator
  fullName: DrawnUi.Draw.ProgressAnimator
- uid: DrawnUi.Draw.RangeAnimator
  commentId: T:DrawnUi.Draw.RangeAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RangeAnimator.html
  name: RangeAnimator
  nameWithType: RangeAnimator
  fullName: DrawnUi.Draw.RangeAnimator
- uid: DrawnUi.Draw.LinearInterpolationTimingParameters
  commentId: T:DrawnUi.Draw.LinearInterpolationTimingParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LinearInterpolationTimingParameters.html
  name: LinearInterpolationTimingParameters
  nameWithType: LinearInterpolationTimingParameters
  fullName: DrawnUi.Draw.LinearInterpolationTimingParameters
- uid: DrawnUi.Draw.RangeVectorAnimator
  commentId: T:DrawnUi.Draw.RangeVectorAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RangeVectorAnimator.html
  name: RangeVectorAnimator
  nameWithType: RangeVectorAnimator
  fullName: DrawnUi.Draw.RangeVectorAnimator
- uid: DrawnUi.Draw.RenderingAnimator
  commentId: T:DrawnUi.Draw.RenderingAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RenderingAnimator.html
  name: RenderingAnimator
  nameWithType: RenderingAnimator
  fullName: DrawnUi.Draw.RenderingAnimator
- uid: DrawnUi.Draw.ScrollFlingAnimator
  commentId: T:DrawnUi.Draw.ScrollFlingAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScrollFlingAnimator.html
  name: ScrollFlingAnimator
  nameWithType: ScrollFlingAnimator
  fullName: DrawnUi.Draw.ScrollFlingAnimator
- uid: DrawnUi.Draw.ScrollFlingVectorAnimator
  commentId: T:DrawnUi.Draw.ScrollFlingVectorAnimator
  href: DrawnUi.Draw.ScrollFlingVectorAnimator.html
  name: ScrollFlingVectorAnimator
  nameWithType: ScrollFlingVectorAnimator
  fullName: DrawnUi.Draw.ScrollFlingVectorAnimator
- uid: DrawnUi.Draw.SkiaValueAnimator
  commentId: T:DrawnUi.Draw.SkiaValueAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaValueAnimator.html
  name: SkiaValueAnimator
  nameWithType: SkiaValueAnimator
  fullName: DrawnUi.Draw.SkiaValueAnimator
- uid: DrawnUi.Draw.SkiaVectorAnimator
  commentId: T:DrawnUi.Draw.SkiaVectorAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaVectorAnimator.html
  name: SkiaVectorAnimator
  nameWithType: SkiaVectorAnimator
  fullName: DrawnUi.Draw.SkiaVectorAnimator
- uid: DrawnUi.Draw.SpringWithVelocityAnimator
  commentId: T:DrawnUi.Draw.SpringWithVelocityAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SpringWithVelocityAnimator.html
  name: SpringWithVelocityAnimator
  nameWithType: SpringWithVelocityAnimator
  fullName: DrawnUi.Draw.SpringWithVelocityAnimator
- uid: DrawnUi.Draw.SpringWithVelocityVectorAnimator
  commentId: T:DrawnUi.Draw.SpringWithVelocityVectorAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SpringWithVelocityVectorAnimator.html
  name: SpringWithVelocityVectorAnimator
  nameWithType: SpringWithVelocityVectorAnimator
  fullName: DrawnUi.Draw.SpringWithVelocityVectorAnimator
- uid: DrawnUi.Draw.SkiaFrame
  commentId: T:DrawnUi.Draw.SkiaFrame
  href: DrawnUi.Draw.SkiaFrame.html
  name: SkiaFrame
  nameWithType: SkiaFrame
  fullName: DrawnUi.Draw.SkiaFrame
- uid: DrawnUi.Draw.SkiaLayer
  commentId: T:DrawnUi.Draw.SkiaLayer
  href: DrawnUi.Draw.SkiaLayer.html
  name: SkiaLayer
  nameWithType: SkiaLayer
  fullName: DrawnUi.Draw.SkiaLayer
- uid: DrawnUi.Draw.SkiaRow
  commentId: T:DrawnUi.Draw.SkiaRow
  href: DrawnUi.Draw.SkiaRow.html
  name: SkiaRow
  nameWithType: SkiaRow
  fullName: DrawnUi.Draw.SkiaRow
- uid: DrawnUi.Draw.SkiaGrid
  commentId: T:DrawnUi.Draw.SkiaGrid
  href: DrawnUi.Draw.SkiaGrid.html
  name: SkiaGrid
  nameWithType: SkiaGrid
  fullName: DrawnUi.Draw.SkiaGrid
- uid: DrawnUi.Draw.SkiaStack
  commentId: T:DrawnUi.Draw.SkiaStack
  href: DrawnUi.Draw.SkiaStack.html
  name: SkiaStack
  nameWithType: SkiaStack
  fullName: DrawnUi.Draw.SkiaStack
- uid: DrawnUi.Draw.SkiaWrap
  commentId: T:DrawnUi.Draw.SkiaWrap
  href: DrawnUi.Draw.SkiaWrap.html
  name: SkiaWrap
  nameWithType: SkiaWrap
  fullName: DrawnUi.Draw.SkiaWrap
- uid: DrawnUi.Draw.Sk3dView
  commentId: T:DrawnUi.Draw.Sk3dView
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.Sk3dView.html
  name: Sk3dView
  nameWithType: Sk3dView
  fullName: DrawnUi.Draw.Sk3dView
- uid: DrawnUi.Draw.SkCamera3D
  commentId: T:DrawnUi.Draw.SkCamera3D
  href: DrawnUi.Draw.SkCamera3D.html
  name: SkCamera3D
  nameWithType: SkCamera3D
  fullName: DrawnUi.Draw.SkCamera3D
- uid: DrawnUi.Draw.SkCamera3D2
  commentId: T:DrawnUi.Draw.SkCamera3D2
  href: DrawnUi.Draw.SkCamera3D2.html
  name: SkCamera3D2
  nameWithType: SkCamera3D2
  fullName: DrawnUi.Draw.SkCamera3D2
- uid: DrawnUi.Draw.SkPatch3D
  commentId: T:DrawnUi.Draw.SkPatch3D
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkPatch3D.html
  name: SkPatch3D
  nameWithType: SkPatch3D
  fullName: DrawnUi.Draw.SkPatch3D
- uid: DrawnUi.Draw.TrackedObject`1
  commentId: T:DrawnUi.Draw.TrackedObject`1
  href: DrawnUi.Draw.TrackedObject-1.html
  name: TrackedObject<T>
  nameWithType: TrackedObject<T>
  fullName: DrawnUi.Draw.TrackedObject<T>
  nameWithType.vb: TrackedObject(Of T)
  fullName.vb: DrawnUi.Draw.TrackedObject(Of T)
  name.vb: TrackedObject(Of T)
  spec.csharp:
  - uid: DrawnUi.Draw.TrackedObject`1
    name: TrackedObject
    href: DrawnUi.Draw.TrackedObject-1.html
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: DrawnUi.Draw.TrackedObject`1
    name: TrackedObject
    href: DrawnUi.Draw.TrackedObject-1.html
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
